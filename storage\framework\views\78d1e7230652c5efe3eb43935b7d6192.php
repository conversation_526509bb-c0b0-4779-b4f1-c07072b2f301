<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/illness.css']); ?>
<div class="illness-container">
    <div class="illness-header">
        <h1 class="illness-title">Những điều cần biết về bệnh</h1>
        <?php echo $__env->make('medical::common.search_bar', [
            'action' => route('medical.illness_category'),
            'method' => 'GET',
            'name' => 'search',
            'placeholder' => 'Tì<PERSON> kiếm thông tin bệnh',
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>

    <!-- Bệnh phổ biến section -->
    <div class="illness-header">
        <h1 class="illness-title">Bệnh phổ biến</h1>
    </div>
    <div class="illness-section">
        <div class="illness-list">
            <?php $__currentLoopData = $featuredIllnesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $illness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('medical.illness_detail', $illness->slug)); ?>" class="illness-card-regular" style="margin-bottom: 20px;">
                <div class="illness-card-img">
                    <img src="<?php echo e($illness->image_url ?? '/images/huyetap.png'); ?>" alt="<?php echo e($illness->name); ?>">
                </div>
                <div class="illness-card-name"><?php echo e($illness->name); ?></div>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'illness-list',
            'childClass' => 'illness-card-regular',
            'perPage' => 7,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
</div>

<!--illness-research-->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bệnh theo đối tượng</h2>
    </div>

    <div class="grid grid-cols-3 gap-4">
        <?php $__currentLoopData = $illnessCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="illness-card illness-card-category">
            <div class="flex">
                <img src="<?php echo e($category->image_url ?? asset('images/default-illness.png')); ?>"
                     alt="<?php echo e($category->name); ?>"
                     onclick="window.location.href='<?php echo e(route('medical.illness_category.show', $category->slug)); ?>'"
                     style="cursor: pointer;">
                <div class="illness-card-content">
                    <h3 onclick="window.location.href='<?php echo e(route('medical.illness_category.show', $category->slug)); ?>'"
                        style="cursor: pointer;"><?php echo e($category->name); ?></h3>
                    <div class="ill-list">
                        <?php $__currentLoopData = $category->illnesses->take(7); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $illness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="illness" onclick="window.location.href='<?php echo e(route('medical.illness_detail', $illness->slug)); ?>'"><?php echo e($illness->name); ?></span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($category->illnesses->count() > 7): ?>
                            <span class="illness" style="color: #F06F22;"><a href="<?php echo e(route('medical.illness_category.show', $category->slug)); ?>" class="text-orange-500 hover:text-orange-600">Xem thêm <i class="fa-solid fa-arrow-right"></i></a></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'grid-cols-3',
            'childClass' => 'illness-card-category',
            'perPage' => 3,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>


<div class="illness-container">
    <div class="illness-header">
        <h2 class="illness-title">Bộ phận cơ thể</h2>
    </div>
    <div class="illness-section">
        <div class="illness-list body-part-list">
            <?php $__currentLoopData = $bodyParts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bodyPart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('medical.illness_category', ['body_part' => $bodyPart->slug])); ?>" class="illness-card body-part">
                <div class="illness-card-img">
                    <img src="<?php echo e($bodyPart->image_url ?? '/images/huyetap.png'); ?>" alt="<?php echo e($bodyPart->name); ?>">
                </div>
                <div class="illness-card-name"><?php echo e($bodyPart->name); ?></div>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'body-part-list',
            'childClass' => 'body-part',
            'perPage' => 7,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>


<div class="illness-container">
    <div class="illness-header">
        <h1 class="illness-title">Nhóm bệnh theo mùa</h1>
    </div>
    <div class="illness-section">
        <div class="illness-list illness-season-list">
            <?php $__currentLoopData = $seasonalIllnesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $illness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('medical.illness_detail', $illness->slug)); ?>" class="illness-card illness-season">
                <div class="illness-card-img">
                    <img src="<?php echo e($illness->image_url ?? '/images/huyetap.png'); ?>" alt="<?php echo e($illness->name); ?>">
                </div>
                <div class="illness-card-name"><?php echo e($illness->name); ?></div>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'illness-season-list',
            'childClass' => 'illness-season',
            'perPage' => 7,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>


<div class="illness-container">
    <div class="illness-header">
        <h2 class="illness-title">Chuyên trang bệnh học</h2>
    </div>
    <div class="illness-section">
        <div class="illness-study-list">
            <?php $__currentLoopData = $studyCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('medical.illness_category.show', $category->slug)); ?>" class="illness-card illness-study">
                <div class="illness-card-img">
                    <img src="<?php echo e($category->image_url ?? '/images/default-category.png'); ?>" alt="<?php echo e($category->name); ?>">
                </div>
                <div class="illness-card-name"><?php echo e($category->name); ?></div>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'illness-study-list',
            'childClass' => 'illness-study',
            'perPage' => 12,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>


<div class="illness-tags-section">
    <div class="illness-tags-title">Nhóm bệnh chuyên khoa</div>
    <div class="illness-tags-list">
        <?php $__currentLoopData = $specialtyCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button class="illness-tag<?php echo e($index === 0 ? ' active' : ''); ?>" data-category-id="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <div class="illness-tags-diseases" id="illness-tags-diseases">
    <?php $__currentLoopData = $category_illness; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cateIllness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a id="<?php echo e($cateIllness->id); ?>" 
           data-category-id="<?php echo e($cateIllness->illness_category_id); ?>" 
           onclick="window.location.href='<?php echo e(route('medical.illness_detail', $cateIllness->slug)); ?>'" 
           class="illness-disease"><?php echo e($cateIllness->name); ?></a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const moreBtn = document.getElementById('illness-tags-more-btn');
        const diseasesList = document.getElementById('illness-tags-diseases');
        let expanded = false;
        
        if (moreBtn && diseasesList) {
            moreBtn.addEventListener('click', function() {
                expanded = !expanded;
                if (expanded) {
                    diseasesList.style.maxHeight = '1000px';
                    moreBtn.textContent = 'Thu gọn';
                } else {
                    diseasesList.style.maxHeight = '320px';
                    moreBtn.textContent = 'Xem thêm';
                }
            });
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        var tags = document.querySelectorAll('.illness-tag');
        var diseasesList = document.getElementById('illness-tags-diseases');

        // Function to filter illness diseases based on category
        function filterIllnessesByCategory(categoryId) {
            var illnessDiseases = document.querySelectorAll('.illness-disease');
            
            illnessDiseases.forEach(function(disease) {
                // Giả sử mỗi illness-disease có data-category-id hoặc id tương ứng với illness_category_id
                // Bạn cần thêm data-category-id vào các thẻ illness-disease trong Blade
                var diseaseCategoryId = disease.dataset.categoryId;
                
                if (categoryId === 'all' || diseaseCategoryId === categoryId) {
                    disease.style.display = 'inline-block'; // hoặc 'block' tùy theo CSS của bạn
                } else {
                    disease.style.display = 'none';
                }
            });
        }

        // Load illnesses for first category on page load
        if (tags.length > 0) {
            // Set first tag as active
            tags[0].classList.add('active');
            var firstCategoryId = tags[0].dataset.categoryId;
            
            // Filter diseases for first category
            filterIllnessesByCategory(firstCategoryId);
            
            // Load illnesses via AJAX if needed
            if (typeof loadIllnesses === 'function') {
                loadIllnesses(firstCategoryId);
            }
        }

        // Xử lý click: chỉ 1 tag active và filter illnesses
        tags.forEach(function(tag) {
            tag.addEventListener('click', function() {
                // Remove active class from all tags
                tags.forEach(function(t) { 
                    t.classList.remove('active'); 
                });
                
                // Add active class to clicked tag
                this.classList.add('active');

                // Get category ID from clicked tag
                var categoryId = this.dataset.categoryId;
                
                // Filter illness diseases based on selected category
                filterIllnessesByCategory(categoryId);

                // Load illnesses via AJAX if the function exists
                if (typeof loadIllnesses === 'function') {
                    loadIllnesses(categoryId);
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/illness/illness.blade.php ENDPATH**/ ?>