<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Thêm font Be Vietnam Pro giống với app layout -->
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(asset('themes/shop/medical/build/assets/signin-THiQSkzI.css')); ?>">
    <!-- jQuery (required for toastr) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <title>Đặt lại mật khẩu - Medical Shop</title>
    
    <style>
        body, input, button, select {
            font-family: 'Be Vietnam Pro', sans-serif !important;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="logo">
            <img src="/images/full_logo_footer.png" alt="Medical Logo">
        </div>

        <div class="login-form">
            <h1>Đặt lại mật khẩu</h1>
            <p style="color: #666; margin-bottom: 20px; font-size: 14px;">
                Nhập mật khẩu mới cho tài khoản của bạn.
            </p>

            <form id="form-reset-password" action="<?php echo e(route('medical.reset_password.store')); ?>" method="POST" novalidate>
                <?php echo csrf_field(); ?>
                <input type="hidden" name="token" value="<?php echo e($token); ?>">

                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" placeholder="Nhập email của bạn"
                        value="<?php echo e(old('email', $email ?? request()->email)); ?>"
                        class="<?php echo e($errors->has('email') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if($errors->has('email')): ?>
                        <span class="error-message"><?php echo e($errors->first('email')); ?></span>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="password">Mật khẩu mới <span class="required">*</span></label>
                    <input type="password" id="password" name="password" placeholder="Nhập mật khẩu mới"
                        class="<?php echo e($errors->has('password') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if($errors->has('password')): ?>
                        <span class="error-message"><?php echo e($errors->first('password')); ?></span>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="password_confirmation">Xác nhận mật khẩu <span class="required">*</span></label>
                    <input type="password" id="password_confirmation" name="password_confirmation" placeholder="Nhập lại mật khẩu mới"
                        class="<?php echo e($errors->has('password_confirmation') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if($errors->has('password_confirmation')): ?>
                        <span class="error-message"><?php echo e($errors->first('password_confirmation')); ?></span>
                    <?php endif; ?>
                </div>

                <div class="form-options">
                    <div class="show-password">
                        <input type="checkbox" id="show-password">
                        <label for="show-password">Hiện mật khẩu</label>
                    </div>
                </div>

                <button type="submit" class="sign-in-btn">Đặt lại mật khẩu</button>

                <div class="create-account">
                    <span>Nhớ mật khẩu?</span>
                    <a href="/signin"><strong>Quay lại đăng nhập</strong></a>
                </div>
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Configure toastr
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "timeOut": "5000",
                "positionClass": "toast-top-right",
                "preventDuplicates": true
            };

            // Show error message
            <?php if(session('error')): ?>
                toastr.error('<?php echo e(session('error')); ?>');
            <?php endif; ?>

            // Password visibility toggle
            const passwordField = document.getElementById('password');
            const passwordConfirmationField = document.getElementById('password_confirmation');
            const showPasswordCheckbox = document.getElementById('show-password');

            showPasswordCheckbox.addEventListener('change', function() {
                const type = this.checked ? 'text' : 'password';
                passwordField.type = type;
                passwordConfirmationField.type = type;
            });

            // Form validation
            $('#form-reset-password').on('submit', function(e) {
                const form = this;
                const emailField = document.getElementById('email');
                let isValid = true;

                // Clear previous errors (only client-side ones)
                document.querySelectorAll('.error-message').forEach(function(element) {
                    if (!element.textContent.includes('<?php echo e($errors->first('email')); ?>') &&
                        !element.textContent.includes('<?php echo e($errors->first('password')); ?>') &&
                        !element.textContent.includes('<?php echo e($errors->first('password_confirmation')); ?>')) {
                        element.remove();
                    }
                });

                // Validate email
                if (!emailField.value.trim()) {
                    showFieldError(emailField, 'Vui lòng nhập email.');
                    isValid = false;
                } else if (!isValidEmail(emailField.value)) {
                    showFieldError(emailField, 'Email không đúng định dạng.');
                    isValid = false;
                }

                // Validate password
                if (!passwordField.value.trim()) {
                    showFieldError(passwordField, 'Vui lòng nhập mật khẩu.');
                    isValid = false;
                } else if (passwordField.value.length < 6) {
                    showFieldError(passwordField, 'Mật khẩu phải có ít nhất 6 ký tự.');
                    isValid = false;
                }

                // Validate password confirmation
                if (!passwordConfirmationField.value.trim()) {
                    showFieldError(passwordConfirmationField, 'Vui lòng xác nhận mật khẩu.');
                    isValid = false;
                } else if (passwordField.value !== passwordConfirmationField.value) {
                    showFieldError(passwordConfirmationField, 'Xác nhận mật khẩu không khớp.');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            function showFieldError(field, message) {
                field.classList.add('is-invalid');
                const errorElement = document.createElement('span');
                errorElement.className = 'error-message';
                errorElement.textContent = message;
                field.parentNode.appendChild(errorElement);
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });
    </script>
</body>

</html>
<?php /**PATH /var/www/html/resources/themes/medical/views/reset-password/reset-password.blade.php ENDPATH**/ ?>