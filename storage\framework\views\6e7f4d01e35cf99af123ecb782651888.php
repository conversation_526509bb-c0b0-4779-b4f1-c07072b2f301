<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/order_detail.css']); ?>
<div class="checkout-container">
    <!-- Cột trái: Thông tin thanh toán -->
    <div class="checkout-left">
        <div class="checkout-section">
            <h2 class="section-title">Thanh toán</h2>
            <!-- product -->
            <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="product-summary">
                <img src="<?php echo e($item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                    alt="Sản phẩm" class="product-img">
                <div class="product-info">
                    <div class="product-name"><?php echo e($item->product->name); ?></div>
                    <div class="product-variant"><?php echo e($item->product->variant ?? 'Vỉ'); ?></div>
                </div>
                <div class="product-qty">x<?php echo e($item->qty_ordered); ?></div>
                <div class="product-price"><?php echo e(number_format($item->price)); ?> đ</div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <div class="checkout-note">
                <label for="note">Ghi chú</label>
                <textarea id="note" placeholder="Ghi chú của đơn hàng" readonly><?php echo e($order->comments->first()->comment ?? ''); ?></textarea>
            </div>
            <div class="checkout-note">
                <div class="address-title">Phương thức thanh toán</div>
                <p>Thanh toán qua QR</p>
            </div>
            <div class="checkout-note" style="margin-top: 2%;">
                <div class="address-title">Phương thức vận chuyển</div>
                <p>Miễn phí vận chuyển</p>
            </div>

        </div>

        <div class="checkout-section">
            <div class="address-info-row">
                <div>
                    <div class="address-title">Thông tin người nhận</div>
                    <div class="address-user">
                        <span class="address-name"><?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->first_name ?? ''); ?> <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->last_name ?? ''); ?></span>
                        <span class="address-phone">| <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->phone ?? ''); ?></span>
                    </div>
                    <div class="address-detail">
                        <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->address ?? ''); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cột phải: Chi tiết thanh toán -->
    <div class="checkout-right">
        <div class="checkout-summary">
            <div class="summary-detail">
                <div class="summary-row">
                    <span>Tạm tính</span>
                    <span><?php echo e(number_format($order->sub_total)); ?> đ</span>
                </div>
                <div class="summary-row">
                    <span>Phí vận chuyển</span>
                    <span><?php echo e(number_format($order->shipping_amount)); ?> đ</span>
                </div>
                <div class="summary-row">
                    <span>Trạng thái đơn hàng</span>
                    <span>
                        <?php
                            $statusMap = [
                                'pending' => 'Chờ xử lý',
                                'processing' => 'Đang xử lý',
                                'completed' => 'Hoàn thành',    
                                'canceled' => 'Đã hủy',
                                'closed' => 'Đã đóng',
                                'refunded' => 'Đã hoàn tiền',
                                'fraud' => 'Gian lận'
                            ];
                            echo $statusMap[$order->status] ?? $order->status;
                        ?>
                    </span>
                </div>
            </div>
            <div class="summary-total">
                <div>
                    <span class="total-label">Tổng tiền</span>
                    <span class="total-note"><?php echo e($order->items->count()); ?> sản phẩm</span>
                </div>
                <div class="total-value"><?php echo e(number_format($order->grand_total)); ?> đ</div>
            </div>
        </div>
    </div>
</div>

<div id="toast-data"
     data-success="<?php echo e(session('success')); ?>"
     data-error="<?php echo e(session('error')); ?>">
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.quick-order-pagination').forEach(function(pagination) {
            pagination.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-order-page-btn')) {
                    const buttons = Array.from(pagination.querySelectorAll('.quick-order-page-btn'))
                        .filter(btn => !btn.hasAttribute('data-page'));
                    // Nếu bấm <<
                    if (e.target.getAttribute('data-page') === 'first') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[0].classList.add('active');
                    }
                    // Nếu bấm >>
                    else if (e.target.getAttribute('data-page') === 'last') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[buttons.length - 1].classList.add('active');
                    }
                    // Nếu bấm số trang
                    else {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');
                    }
                }
            });
        });

        const toastData = document.getElementById('toast-data');
        if (toastData) {
            const success = toastData.dataset.success;
            const error = toastData.dataset.error;
            if (success) {
                Toastify({
                    text: success,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#059669",
                    close: true
                }).showToast();
            }
            if (error) {
                Toastify({
                    text: error,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();
            }
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/order_detail/order_detail.blade.php ENDPATH**/ ?>