# Cart Rule Performance Optimization

## Vấn đề
Trang tạo Cart Rules (`/admin/marketing/promotions/cart-rules/create`) bị timeout do load quá nhiều dữ liệu cùng lúc:

- Toàn bộ category tree
- Tất cả attributes và options
- Payment methods, shipping methods
- Countries, states
- Tax categories, attribute families

## Giải pháp đã triển khai

### 1. Lazy Loading cho Categories
- Categories không được load ngay khi trang tải
- Sử dụng AJAX để load khi cần thiết
- Endpoint: `/admin/marketing/promotions/cart-rules/categories`

### 2. Tối ưu hóa Attribute Loading
- Chỉ load thông tin cơ bản của attributes
- Options chỉ load cho datasets nhỏ (< 100 items)
- Datasets lớn sử dụng lazy loading
- Endpoint: `/admin/marketing/promotions/cart-rules/attribute-options/{id}`

### 3. Caching
- Cache condition attributes trong 1 giờ
- Giảm database queries
- Command để clear cache: `php artisan cart-rule:clear-cache`

### 4. Database Optimization
- Chỉ select các columns cần thiết
- Giới hạn eager loading
- Tối ưu hóa queries

### 5. Timeout & Memory Management
- Tăng max_execution_time lên 300 giây (5 phút)
- Tăng memory_limit lên 512M cho cart rule pages
- Middleware: `OptimizeCartRuleTimeout`

## Cách sử dụng

### 1. Cấu hình Environment Variables
```env
# Cache settings
CART_RULE_CACHE_ENABLED=true
CART_RULE_CACHE_TTL=3600

# Lazy loading
CART_RULE_LAZY_LOADING_ENABLED=true
CART_RULE_LAZY_LOAD_CATEGORIES=true
CART_RULE_LAZY_LOAD_ATTRIBUTES=true
CART_RULE_ATTRIBUTE_OPTIONS_THRESHOLD=100

# Timeout settings
CART_RULE_MAX_EXECUTION_TIME=300
CART_RULE_MEMORY_LIMIT=512M

# Database optimization
CART_RULE_SELECT_ONLY_NEEDED=true
CART_RULE_LIMIT_EAGER_LOADING=true
```

### 2. Publish Config File
```bash
php artisan vendor:publish --tag=cart-rule-config
```

### 3. Clear Cache khi cần
```bash
php artisan cart-rule:clear-cache
```

### 4. Đăng ký Service Provider
Thêm vào `config/app.php`:
```php
'providers' => [
    // ...
    Webkul\CartRule\Providers\CartRuleOptimizationServiceProvider::class,
],
```

### 5. Đăng ký Middleware
Thêm vào `app/Http/Kernel.php`:
```php
protected $routeMiddleware = [
    // ...
    'optimize-cart-rule' => \Webkul\Admin\Http\Middleware\OptimizeCartRuleTimeout::class,
];
```

## Monitoring & Debugging

### 1. Kiểm tra Cache
```php
// Kiểm tra cache có tồn tại không
Cache::has('cart_rule_condition_attributes');

// Xem nội dung cache
Cache::get('cart_rule_condition_attributes');
```

### 2. Kiểm tra Performance
- Sử dụng Laravel Debugbar
- Monitor database queries
- Kiểm tra memory usage

### 3. Logs
- Check Laravel logs cho errors
- Monitor AJAX requests trong browser DevTools

## Lợi ích

1. **Giảm thời gian load trang**: Từ timeout xuống < 5 giây
2. **Giảm memory usage**: Chỉ load dữ liệu cần thiết
3. **Cải thiện UX**: Trang load nhanh hơn, responsive hơn
4. **Scalable**: Hoạt động tốt với datasets lớn
5. **Configurable**: Có thể tùy chỉnh qua config

## Lưu ý

1. **Cache Invalidation**: Cần clear cache khi có thay đổi categories/attributes
2. **AJAX Dependencies**: Cần đảm bảo JavaScript hoạt động đúng
3. **Fallback**: Có fallback cho trường hợp AJAX fail
4. **Browser Compatibility**: Test trên các browsers khác nhau

## Troubleshooting

### Vẫn bị timeout?
1. Tăng `CART_RULE_MAX_EXECUTION_TIME`
2. Tăng `CART_RULE_MEMORY_LIMIT`
3. Kiểm tra database indexes
4. Enable query caching

### AJAX không hoạt động?
1. Kiểm tra CSRF token
2. Kiểm tra routes đã đăng ký
3. Check browser console cho errors
4. Verify endpoints trả về đúng format

### Cache không hoạt động?
1. Kiểm tra cache driver configuration
2. Verify cache permissions
3. Check cache storage space
4. Test cache manually
