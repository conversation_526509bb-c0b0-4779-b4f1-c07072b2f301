/**
 * Cart Rule Lazy Loader
 * Handles lazy loading of categories and attribute options for cart rules
 */
class CartRuleLazyLoader {
    constructor() {
        this.cache = new Map();
        this.loadingStates = new Map();
    }

    /**
     * Load categories via AJAX
     */
    async loadCategories() {
        const cacheKey = 'categories';
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (this.loadingStates.get(cacheKey)) {
            return this.loadingStates.get(cacheKey);
        }

        const loadingPromise = this.fetchData('/admin/marketing/promotions/cart-rules/categories');
        this.loadingStates.set(cacheKey, loadingPromise);

        try {
            const data = await loadingPromise;
            this.cache.set(cacheKey, data);
            this.loadingStates.delete(cacheKey);
            return data;
        } catch (error) {
            this.loadingStates.delete(cacheKey);
            throw error;
        }
    }

    /**
     * Load attribute options via AJAX
     */
    async loadAttributeOptions(attributeId) {
        const cacheKey = `attribute_options_${attributeId}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (this.loadingStates.get(cacheKey)) {
            return this.loadingStates.get(cacheKey);
        }

        const loadingPromise = this.fetchData(`/admin/marketing/promotions/cart-rules/attribute-options/${attributeId}`);
        this.loadingStates.set(cacheKey, loadingPromise);

        try {
            const data = await loadingPromise;
            this.cache.set(cacheKey, data);
            this.loadingStates.delete(cacheKey);
            return data;
        } catch (error) {
            this.loadingStates.delete(cacheKey);
            throw error;
        }
    }

    /**
     * Fetch data from API
     */
    async fetchData(url) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || 'Failed to load data');
            }

            return result.data;
        } catch (error) {
            console.error('Failed to fetch data:', error);
            throw error;
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        this.loadingStates.clear();
    }

    /**
     * Show loading indicator
     */
    showLoading(element) {
        if (element) {
            element.innerHTML = '<option value="">Loading...</option>';
            element.disabled = true;
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoading(element) {
        if (element) {
            element.disabled = false;
        }
    }

    /**
     * Populate select element with options
     */
    populateSelect(element, options, placeholder = 'Select...') {
        if (!element) return;

        element.innerHTML = `<option value="">${placeholder}</option>`;
        
        if (Array.isArray(options)) {
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id || option.value;
                optionElement.textContent = option.name || option.label || option.admin_name;
                element.appendChild(optionElement);
            });
        }
    }

    /**
     * Handle tree structure for categories
     */
    populateTree(container, categories) {
        if (!container || !Array.isArray(categories)) return;

        const buildTree = (items, level = 0) => {
            return items.map(item => {
                const indent = '&nbsp;'.repeat(level * 4);
                const hasChildren = item.children && item.children.length > 0;
                
                let html = `<option value="${item.id}">${indent}${item.name}</option>`;
                
                if (hasChildren) {
                    html += buildTree(item.children, level + 1);
                }
                
                return html;
            }).join('');
        };

        container.innerHTML = '<option value="">Select Category...</option>' + buildTree(categories);
    }
}

// Export for use in other modules
window.CartRuleLazyLoader = CartRuleLazyLoader;
