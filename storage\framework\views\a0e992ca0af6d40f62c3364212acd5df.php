<div class="container px-[60px] max-1180:px-0">
    <div class="mt-12 flex gap-10 max-1180:flex-wrap max-lg:mt-0 max-sm:gap-y-6">

        <?php if (isset($component)) { $__componentOriginala9edc0e71c40cffba9a9bc821f0a4093 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9edc0e71c40cffba9a9bc821f0a4093 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.shimmer.products.gallery','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::shimmer.products.gallery'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9edc0e71c40cffba9a9bc821f0a4093)): ?>
<?php $attributes = $__attributesOriginala9edc0e71c40cffba9a9bc821f0a4093; ?>
<?php unset($__attributesOriginala9edc0e71c40cffba9a9bc821f0a4093); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9edc0e71c40cffba9a9bc821f0a4093)): ?>
<?php $component = $__componentOriginala9edc0e71c40cffba9a9bc821f0a4093; ?>
<?php unset($__componentOriginala9edc0e71c40cffba9a9bc821f0a4093); ?>
<?php endif; ?>

        <div class="relative max-w-[590px] max-1180:w-full max-1180:max-w-full max-1180:px-5">
            <div class="flex justify-between gap-4">
                <h1 class="shimmer h-[46px] w-2/4"></h1>

                <div class="shimmer h-[46px] w-[46px] rounded-full"></div>
            </div>

            <div class="shimmer mt-4 h-[38px] w-[150px] rounded-lg"></div>

            <p class="shimmer mt-5 h-9 w-[35%]"></p>

            <div class="mt-6 grid gap-2.5 max-sm:my-[20px] max-sm:flex max-sm:justify-between">
                <p class="shimmer h-[27px] w-full max-sm:h-[21px] max-sm:w-[100px]"></p>
                <p class="shimmer h-[27px] w-[90%] max-sm:hidden"></p>
                <p class="shimmer h-[27px] w-4/5 max-sm:h-[21px] max-sm:w-[60px]"></p>
            </div>

            <!-- Colors -->
            <div class="mt-5">
                <h3 class="shimmer mb-4 h-9 w-1/5"></h3>

                <div class="flex items-center space-x-3">
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                    <span class="shimmer h-8 w-8 rounded-full"></span>
                </div>
            </div>

            <!-- Size -->
            <div class="mt-5">
                <h3 class="shimmer mb-4 h-9 w-1/5"></h3>

                <div class="flex flex-wrap gap-3">
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                    <span class="shimmer h-[60px] w-[60px] rounded-full"></span>
                </div>
            </div>

            <div class="mt-8 flex max-w-[470px] flex-wrap gap-4">
                <!-- Quantity changer button -->
                <div class="shimmer h-14 w-[161px] rounded-xl max-sm:w-[124px]"></div>

                <!-- Add to cart Button -->
                <button class="shimmer h-14 w-[279px] rounded-xl"></button>
            </div>

            <!-- Buy Now Button -->
            <button class="shimmer mt-5 h-14 w-full rounded-xl"></button>

            <!-- Share Buttons -->
            <div class="mt-10 flex items-center gap-9 max-sm:flex-wrap">
                <div class="shimmer h-6 w-20"></div>

                <div class="flex items-center gap-6 max-sm:flex-wrap">
                    <div class="shimmer h-6 w-20"></div>
                    
                    <div class="flex gap-3">
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                        <span class="shimmer h-10 w-10 rounded-full"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH /var/www/html/packages/Webkul/Shop/src/Resources/views/components/shimmer/products/view.blade.php ENDPATH**/ ?>