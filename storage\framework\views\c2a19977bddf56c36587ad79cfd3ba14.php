<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/brands_detail.css', 'resources/themes/medical/js/product-card.js']); ?>
<div class="our-product-page">

    <!-- PHẦN DANH CHI TIẾT THƯƠNG HIỆU -->
    <div class="ingredient-detail-card">
        <div class="ingredient-detail-img">
            <?php if($brandInfo && $brandInfo->brand_image): ?>
                <img src="<?php echo e(asset('storage/' . $brandInfo->brand_image)); ?>" alt="<?php echo e($brand ? $brand->admin_name : 'Brand'); ?>" />
            <?php else: ?>
                <img src="/images/th1.png" alt="<?php echo e($brand ? $brand->admin_name : 'Brand'); ?>" />
            <?php endif; ?>
        </div>
        <div class="ingredient-detail-info">
            <div class="ingredient-detail-title"><?php echo e($brand ? $brand->admin_name : ($brandName ?: 'Thương hiệu')); ?></div>
            <div class="ingredient-detail-desc">
                <?php if($brandInfo && $brandInfo->brand_description): ?>
                    <?php echo e($brandInfo->brand_description); ?>

                <?php else: ?>
                    Thông tin chi tiết về thương hiệu đang được cập nhật. Vui lòng liên hệ với chúng tôi để biết thêm thông tin.
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- PHẦN DƯỚI: BỘ LỌC + SẢN PHẨM -->
    <div class="our-product-main">
        <!-- BỘ LỌC (STICKY) -->
        <aside class="our-product-filter">
            <form method="GET" action="<?php echo e(url()->current()); ?>">
                <div class="filter-section">
                    <div class="filter-title">Bộ lọc</div>
                    <a href="<?php echo e(url()->current()); ?>" class="filter-reset">Thiết lập lại</a>
                </div>
                <div class="filter-section">
                    <div class="our-product-sort-bar">
                        <label for="sort-select">Sắp xếp:</label>
                        <select id="sort-select" name="sort">
                            <option value="name" <?php echo e($sortBy == 'name' ? 'selected' : ''); ?>>Tên A-Z</option>
                            <option value="name-desc" <?php echo e($sortBy == 'name-desc' ? 'selected' : ''); ?>>Tên Z-A</option>
                            <option value="price-asc" <?php echo e($sortBy == 'price-asc' ? 'selected' : ''); ?>>Giá tăng dần</option>
                            <option value="price-desc" <?php echo e($sortBy == 'price-desc' ? 'selected' : ''); ?>>Giá giảm dần</option>
                        </select>
                    </div>
                    <label>Khoảng giá</label>
                    <div class="filter-price-options">
                        <label><input type="radio" name="price_range" value="1" <?php echo e($priceRange == '1' ? 'checked' : ''); ?>> Dưới 100.000 đ</label>
                        <label><input type="radio" name="price_range" value="2" <?php echo e($priceRange == '2' ? 'checked' : ''); ?>> 100.000 - 300.000 đ</label>
                        <label><input type="radio" name="price_range" value="3" <?php echo e($priceRange == '3' ? 'checked' : ''); ?>> 300.000 - 500.000 đ</label>
                        <label><input type="radio" name="price_range" value="4" <?php echo e($priceRange == '4' ? 'checked' : ''); ?>> Trên 500.000 đ</label>
                    </div>
                    <button type="submit" class="filter-apply-btn">Áp dụng</button>
                </div>
            </form>
        </aside>

        <!-- SẢN PHẨM -->
        <section class="our-product-list-section">
            <?php if($products && $products->count() > 0): ?>
                <div class="product-count">
                    Tìm thấy <?php echo e($products->count()); ?> sản phẩm
                </div>
                <div class="our-product-list">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('medical::common.product_card', [
                                'product' => $product
                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="no-products">
                    <p>Không tìm thấy sản phẩm nào cho thương hiệu này.</p>
                </div>
            <?php endif; ?>
            <div class="ingredient-pagination">
                <?php echo e($products->links('vendor.pagination.custom')); ?>

            </div>
        </section>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to add/set page=1 to form
    function addPageResetToForm(form) {
        let pageInput = form.querySelector('input[name="page"]');

        if (pageInput) {
            // Nếu đã có input page, set về 1
            pageInput.value = '1';
        } else {
            // Nếu chưa có, tạo input hidden mới
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'page';
            hiddenInput.value = '1';
            form.appendChild(hiddenInput);
        }
    }

    // Handle form submit (nút "Áp dụng")
    const filterForm = document.querySelector('aside.our-product-filter form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            addPageResetToForm(this);
        });
    }

    // Handle sort select change - override inline onchange
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        // Remove inline onchange to avoid conflict
        sortSelect.removeAttribute('onchange');

        // Add new event listener with page reset
        sortSelect.addEventListener('change', function() {
            const form = this.closest('form');
            if (form) {
                addPageResetToForm(form);
                form.submit();
            }
        });
    }

    // Handle price range radio change (optional - for immediate feedback)
    const priceRadios = document.querySelectorAll('input[name="price_range"]');
    priceRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Không auto-submit, chỉ đánh dấu form đã thay đổi
            // User vẫn cần click "Áp dụng"
        });
    });
});
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/brands_detail/brands_detail.blade.php ENDPATH**/ ?>