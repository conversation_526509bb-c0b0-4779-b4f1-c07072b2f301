<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class OptimizeCartRuleTimeout
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Increase timeout for cart rule pages
        if ($this->isCartRulePage($request)) {
            ini_set('max_execution_time', 300); // 5 minutes
            ini_set('memory_limit', '512M');
        }

        return $next($request);
    }

    /**
     * Check if current request is for cart rule pages.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function isCartRulePage(Request $request): bool
    {
        $path = $request->path();
        
        return str_contains($path, 'admin/marketing/promotions/cart-rules');
    }
}
