<?php
$channel = core()->getCurrentChannel();
$footerLinks = app('Webkul\\Theme\\Repositories\\ThemeCustomizationRepository')
->findWhere([
'channel_id' => $channel->id,
'theme_code' => 'medical',
'type' => 'footer_links',
'status' => 1
]);
$footerOptions = [];
if (count($footerLinks)) {
$translation = $footerLinks[0]->translations->first();
if ($translation) {
$footerOptions = is_array($translation->options)
? $translation->options
: json_decode($translation->options, true);
}
}
?>

<footer class="footer-container">
    <div class="footer-main container">
        <div class="container-fluid">
            <div class="row">
                <!-- Company Info -->
                <div class="col-12 col-lg-3 flex flex-col gap-[10px]">
                    <div onclick="window.location.href='/home'" class="logo-container">
                        <img src="/images/full_logo_footer.png" alt="Logo Phan Anh" class="suggestion-logo" style="
                            background-color: white;
                            height: 47px;">
                    </div>
                    <div class="company-name">CÔNG TY CỔ PHẦN DƯỢC VẬT TƯ Y TẾ PHAN ANH</div>
                    <div class="company-details">Giấy chứng nhận ĐKKD do Sở KH&ĐT cấp ngày 16/12/2008</div>
                    <div class="company-details">Trụ sở: Số 28, đường Hùng Vương 3, Phường Hoàng Văn Thụ, Thành phố Bắc Giang, Tỉnh Bắc Giang, Việt Nam.</div>
                    <div class="company-details">
                        Website: <a href="https://phananh.com.vn/" class="website-link">https://phananh.com.vn/</a>
                    </div>
                </div>

                <!-- Column 1: Về thuocphananh.com -->
                <?php if(!empty($footerOptions['column_1'])): ?>
                <div class="col-12 col-lg-3 footer-section">
                    <div class="section-title">Về thuocphananh.com</div>
                    <div class="section-content">
                        <?php $__currentLoopData = $footerOptions['column_1']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($link['url'] ?? '#'); ?>">
                            <?php echo e($link['title'] ?? ''); ?>

                        </a>
                        <br>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Column 2: Chính sách hỗ trợ -->
                <?php if(!empty($footerOptions['column_2'])): ?>
                <div class="col-12 col-lg-3 footer-section">
                    <div class="section-title">Chính sách hỗ trợ</div>
                    <div class="section-content">
                        <?php $__currentLoopData = $footerOptions['column_2']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($link['url'] ?? '#'); ?>">
                            <?php echo e($link['title'] ?? ''); ?>

                        </a>
                        <br>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Column 3: Liên kết - Always show -->
                <div class="col-12 col-lg-3 footer-section">
                    <div class="section-title">Liên kết</div>
                    <div class="section-content link-content">
                        <?php
                        // Lấy links từ column_3 nếu có và sắp xếp theo sort_order
                        $column3Links = [];
                        if (!empty($footerOptions['column_3'])) {
                        $column3Links = $footerOptions['column_3'];
                        // Sắp xếp theo sort_order
                        usort($column3Links, function ($a, $b) {
                        return ($a['sort_order'] ?? 0) - ($b['sort_order'] ?? 0);
                        });
                        }
                        ?>

                        <?php if(!empty($column3Links)): ?>
                        <?php $__currentLoopData = $column3Links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="social-link">
                            <?php
                            $title = $link['title'] ?? '';
                            $url = $link['url'] ?? '#';
                            $icon = '';

                            // Auto-detect icon based on title or URL
                            if (stripos($title, 'facebook') !== false || stripos($url, 'facebook') !== false) {
                            $icon = '<img style="border-radius: 100%;" class="w-6 h-6" src="/images/facebook.svg" />';
                            } elseif (stripos($title, 'zalo') !== false || stripos($url, 'zalo') !== false) {
                            $icon = '<img class="w-6 h-6" src="/images/zalo.png" />';
                            } elseif (stripos($title, 'hotline') !== false || stripos($title, 'phone') !== false || stripos($url, 'tel:') !== false) {
                            $icon = '<i class="fa-solid fa-phone-volume contact-icon-footer" style="color: #FF6B00;"></i>';
                            } elseif (stripos($title, 'email') !== false || stripos($url, 'mailto:') !== false) {
                            $icon = '<i class="fa-solid fa-envelope contact-icon-footer" style="color: #FF6B00;"></i>';
                            } elseif (stripos($title, 'website') !== false || stripos($url, 'http') !== false) {
                            $icon = '<i class="fa-solid fa-globe contact-icon-footer" style="color: #FF6B00;"></i>';
                            }
                            ?>

                            <?php if($icon): ?>
                            <?php echo $icon; ?>

                            <?php endif; ?>

                            <?php if(stripos($title, 'hotline') !== false || stripos($title, 'phone') !== false || stripos($url, 'tel:') !== false): ?>
                            
                            <span class="company-details">
                                <?php echo e($title); ?>

                            </span>
                            <?php else: ?>
                            
                            <a href="<?php echo e($url); ?>" class="company-details">
                                <?php echo e($title); ?>

                            </a>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                    <div class="footer-logo">
                        <img src="/images/bocongthuong.png" alt="Logo Bo Cong Thuong" class="suggestion-logo" style="height: 47px;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Đường kẻ ngang -->
    <div class="footer-divider"></div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="bottom-company-name">CÔNG TY CỔ PHẦN DƯỢC VẬT TƯ Y TẾ PHAN ANH</div>
        <div class="bottom-text">Đơn vị sở hữu, vận hành và cung cấp dịch vụ thương mại điện tử B2B Dược Phan Anh – kết nối trực tiếp đến nhà thuốc, đại lý và cơ sở y tế trên toàn quốc.</div>
        <div class="bottom-text">Địa chỉ: Số 26, ngõ 55 đường Nguyễn Văn Cừ, Phường Ngô Quyền, Thành phố Bắc Giang, Tỉnh Bắc Giang, Việt Nam.</div>
        <div class="bottom-text">Người đại diện: Ths.Bs Phan Anh</div>
        <div class="bottom-text">Mã số thuế: TDB</div>
        <div class="bottom-text">
            ©2025 – Bản quyền thuộc về
            <span class="bottom-text-bold">Công Ty Cổ Phần Dược Vật Tư Y Tế Phan Anh.</span>
        </div>
        <div class="bottom-text">Nghiêm cấm sao chép, sử dụng lại nội dung hoặc hình ảnh từ website khi chưa có sự đồng ý bằng văn bản từ công ty.</div>
    </div>
</footer><?php /**PATH /var/www/html/resources/themes/medical/views/layouts/footer.blade.php ENDPATH**/ ?>