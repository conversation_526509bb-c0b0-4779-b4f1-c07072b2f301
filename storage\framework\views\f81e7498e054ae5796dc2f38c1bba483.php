<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cost_detail.css']); ?>
<div class="checkout-container">
    <!-- Cột trái: Thông tin thanh toán -->
    <div class="checkout-left">
        <div class="checkout-section">
            <h2 class="section-title">Thanh toán</h2>
            <!-- product -->
            <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="product-summary">
                <img src="<?php echo e($item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                    alt="Sản phẩm" class="product-img">
                <div class="product-info">
                    <div class="product-name"><?php echo e($item->product->name); ?></div>
                    <div class="product-variant"><?php echo e($item->product->variant ?? 'Vỉ'); ?></div>
                </div>
                <div class="product-qty">x<?php echo e($item->qty_ordered); ?></div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <div class="quick-order-pagination">
                <button class="quick-order-page-btn" data-page="first">&laquo;</button>
                <button class="quick-order-page-btn active">1</button>
                <button class="quick-order-page-btn">2</button>
                <button class="quick-order-page-btn">3</button>
                <button class="quick-order-page-btn" data-page="last">&raquo;</button>
            </div>

            <div class="checkout-note">
                <label for="note">Ghi chú</label>
                <textarea id="note" placeholder="Ghi chú đơn hàng"></textarea>
            </div>
            <div class="checkout-note">
                <div class="address-title">Phương thức thanh toán</div>
                <p>Thanh toán qua QR</p>
            </div>
            <div class="checkout-note" style="margin-top: 2%;">
                <div class="address-title">Phương thức vận chuyển</div>
                <p>Vận chuyển tiêu chuẩn</p>
            </div>

        </div>

        <div class="checkout-section">
            <div class="address-info-row">
                <div>
                    <div class="address-title">Thông tin người nhận</div>
                    <div class="address-user">
                        <span class="address-name"><?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->first_name ?? ''); ?> <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->last_name ?? ''); ?></span>
                        <span class="address-phone">| <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->phone ?? ''); ?></span>
                    </div>
                    <div class="address-detail">
                        <?php echo e($order->addresses->where('address_type', 'order_shipping')->first()->address ?? ''); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cột phải: Chi tiết thanh toán -->
    <div class="checkout-right">
        <div class="checkout-summary">
            <div class="summary-row summary-promo">
                <span>Khuyến mãi</span>
                <a href="#" class="choose-promo">Chọn mã</a>
            </div>
            <div class="summary-row summary-pxu">
                <span><i class="fa fa-coins"></i> Dùng P-Xu Vàng</span>
                <span class="choose-pxu">Tùy chọn</span>
            </div>
            <div class="summary-row summary-vat">
                <span>Hóa đơn VAT</span>
                <a href="#" class="choose-vat">Yêu cầu xuất hoá đơn</a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.quick-order-pagination').forEach(function(pagination) {
            pagination.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-order-page-btn')) {
                    const buttons = Array.from(pagination.querySelectorAll('.quick-order-page-btn'))
                        .filter(btn => !btn.hasAttribute('data-page'));
                    // Nếu bấm <<
                    if (e.target.getAttribute('data-page') === 'first') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[0].classList.add('active');
                    }
                    // Nếu bấm >>
                    else if (e.target.getAttribute('data-page') === 'last') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[buttons.length - 1].classList.add('active');
                    }
                    // Nếu bấm số trang
                    else {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');
                    }
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/cost_detail/cost_detail.blade.php ENDPATH**/ ?>