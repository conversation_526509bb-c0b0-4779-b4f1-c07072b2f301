<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cost.css', 'resources/themes/medical/css/quote_list.css']); ?>

<div class="quick-order-container">
    <h1 class="quick-order-title">Danh sách báo giá của tôi</h1>

    <div class="quote-list-container">
        <?php if($quotes->count() > 0): ?>
            <table class="quote-table">
                <thead>
                    <tr>
                        <th>Mã báo giá</th>
                        <th>Số lượng</th>
                        <th>Sản phẩm</th>
                        <th>Trạng thái</th>
                        <th>Ngày Báo</th>
                        <th>Chi tiết</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $quotes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quote): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>#<?php echo e($quote->id); ?></td>
                        <td><?php echo e($quote->items->count()); ?></td>
                        <td class="product-thumbnails">
                            <?php $__currentLoopData = $quote->items->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <img src="<?php echo e($item->product && $item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                                     alt="<?php echo e($item->name); ?>"
                                     title="<?php echo e($item->name); ?>"
                                     class="product-thumbnail">
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($quote->items->count() > 3): ?>
                                <span class="more-items">+<?php echo e($quote->items->count() - 3); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="quote-status <?php echo e($quote->status == 'submitted' ? 'status-pending' : 'status-' . $quote->status); ?>">
                                <?php echo e($quote->status == 'submitted' ? 'Đang xử lý' : ucfirst($quote->status)); ?>

                            </span>
                        </td>
                        <td><?php echo e($quote->updated_at->format('d/m/Y H:i')); ?></td>
                        <td>
                            <a href="/cost_detail/<?php echo e($quote->id); ?>" class="view-detail-btn">
                                <i class="fa-solid fa-eye"></i> Xem
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="empty-quote">
                <p>Bạn chưa có báo giá nào</p>
                <a href="/quote" class="continue-shopping-btn">Tạo báo giá mới</a>
            </div>
        <?php endif; ?>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/cost/quote_list.blade.php ENDPATH**/ ?>