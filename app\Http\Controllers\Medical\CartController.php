<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Webkul\Checkout\Facades\Cart;
use Webkul\Product\Repositories\ProductRepository;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Support\Facades\Auth;
use App\Helpers\Toast;
use Illuminate\Support\Facades\Log;
use Webkul\Checkout\Models\CartItem;
use Webkul\Product\Models\ProductFlat;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Medical\ProductController;
use Webkul\Product\Models\Product;


class CartController extends Controller
{
    use ValidatesRequests;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository,
        protected ProductController $productController
    ) {}

    public function index()
    {
        $cart = Cart::getCart();

        // Lấy recently viewed products (5 sản phẩm)
        $customer = auth('customer')->user();
        $recentlyViewedProducts = collect();

        if ($customer) {
            // User đã đăng nhập - lấy từ database
            $recentlyViewedRecords = \App\Models\RecentlyViewedProduct::with([
                'product.images',
                'product.categories',
                'product.attribute_values',
                'product.product_flats',
                'product.variants'
            ])->forCustomer($customer->id)->recent(5)->get();
            $products = $recentlyViewedRecords->pluck('product')->filter();
        } else {
            // Guest user - lấy từ session
            $recentlyViewedIds = session('recently_viewed_products', []);

            if (!empty($recentlyViewedIds)) {
                $productIds = array_slice($recentlyViewedIds, 0, 5);
                $productsFromDb = Product::with(['images', 'categories', 'attribute_values', 'product_flats', 'variants'])
                    ->whereIn('id', $productIds)
                    ->get();

                // Sắp xếp theo thứ tự trong session (mới nhất trước)
                $products = collect($recentlyViewedIds)->map(function ($id) use ($productsFromDb) {
                    return $productsFromDb->firstWhere('id', $id);
                })->filter()->take(5);
            } else {
                $products = collect();
            }
        }

        // Xử lý dữ liệu sản phẩm giống như HomeController và OurProductController
        if ($products->isNotEmpty()) {
            $recentlyViewedProducts = $this->productController->processProductData($products);
        }

        return view('medical::cart.cart', [
            'cart' => $cart,
            'cartItems' => $cart ? $cart->items : collect([]),
            'cartCount' => $cart ? $cart->items->count() : 0,
            'recentlyViewedProducts' => $recentlyViewedProducts
        ]);
    }

    /**
     * Cập nhật số lượng
     */
    public function updateQuantity(Request $request): JsonResponse
    {
        $this->validate($request, [
            'item_id' => 'required|integer',
            'quantity' => 'required|numeric|integer|min:1'
        ], [
            'quantity.required' => 'Vui lòng nhập số lượng.',
            'quantity.numeric' => 'Số lượng phải là một số.',
            'quantity.integer' => 'Số lượng phải là một số nguyên.',
            'quantity.min' => 'Số lượng phải ít nhất là 1.',
        ]);

        try {
            $cart = Cart::getCart();

            if (!$cart) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giỏ hàng',
                    'toast' => [
                        'type' => 'error',
                        'message' => 'Không tìm thấy giỏ hàng'
                    ]
                ]);
            }

            $cartItem = $cart->items()->find($request->item_id);

            if (!$cartItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm trong giỏ hàng',
                    'toast' => [
                        'type' => 'error',
                        'message' => 'Không tìm thấy sản phẩm trong giỏ hàng'
                    ]
                ]);
            }

            // Cập nhật số lượng và tổng tiền
            $cartItem->quantity = $request->quantity;
            $cartItem->total = $cartItem->price * $request->quantity;
            $cartItem->base_total = $cartItem->base_price * $request->quantity;
            $cartItem->save();

            // Cập nhật lại cart
            $cart->refresh();

            // Cập nhật tổng tiền của cart
            $cart->sub_total = $cart->items->sum('total');
            $cart->base_sub_total = $cart->items->sum('base_total');
            $cart->grand_total = $cart->sub_total;
            $cart->base_grand_total = $cart->base_sub_total;
            $cart->save();

            return response()->json([
                'success' => true,
                'message' => 'Cập nhật số lượng thành công!',
                'toast' => Toast::success('Cập nhật số lượng thành công!'),
                'cart' => $cart
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể cập nhật số lượng. Vui lòng thử lại sau.',
                'toast' => Toast::error('Không thể cập nhật số lượng. Vui lòng thử lại sau.')
            ]);
        }
    }

    /**
     * Sync số lượng tạm với DB
     */
    public function syncQuantities(): JsonResponse
    {
        try {
            $cart = Cart::getCart();
            $tempQuantities = session('cart_temp_quantities', []);

            if ($cart && !empty($tempQuantities)) {
                foreach ($tempQuantities as $itemId => $quantity) {
                    $data = ['quantity' => $quantity];
                    Cart::updateItems($data);
                }

                // Xóa số lượng tạm sau khi sync
                session()->forget('cart_temp_quantities');

                return response()->json([
                    'success' => true,
                    'message' => 'Cập nhật giỏ hàng thành công!',
                    'toast' => Toast::success('Cập nhật giỏ hàng thành công!')
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Không có thay đổi nào cần cập nhật',
                'toast' => Toast::info('Không có thay đổi nào cần cập nhật')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể cập nhật giỏ hàng. Vui lòng thử lại sau.',
                'toast' => Toast::error('Không thể cập nhật giỏ hàng. Vui lòng thử lại sau.')
            ]);
        }
    }

    /**
     * Add product to cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {


        $this->validate($request, [
            'product_id' => 'required|integer|exists:products,id',
            'quantity' => 'required|numeric|integer|min:1',
            'unit_type' => 'nullable|integer'
        ], [
            'quantity.required' => 'Vui lòng nhập số lượng.',
            'quantity.numeric' => 'Số lượng phải là một số.',
            'quantity.integer' => 'Số lượng phải là một số nguyên.',
            'quantity.min' => 'Số lượng phải ít nhất là 1.',
        ]);

        try {
            if (!Auth::guard('customer')->check()) {
                session()->put('url.intended', url()->previous());
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng',
                    'redirect' => route('signin'),
                    'toast' => Toast::error('Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng')
                ]);
            }

            $product = $this->productRepository->findOrFail($request->product_id);

            // Nếu là configurable thì tìm variant con đúng unit_type
            if ($product->type == 'configurable' && $request->has('unit_type')) {
                $unitTypeAttrId = DB::table('attributes')->where('code', 'unit_type')->value('id');
                $unitTypeValue = (int) $request->unit_type;
                $variant = $product->variants->filter(function($variant) use ($unitTypeAttrId, $unitTypeValue) {
                    $attrValue = $variant->attribute_values->where('attribute_id', $unitTypeAttrId)->first();
                    return $attrValue && (int)$attrValue->integer_value === $unitTypeValue;
                })->first();
                if (!$variant) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Không tìm thấy phân loại phù hợp!'
                    ], 400);
                }
                $productToAdd = $variant;
            } else {
                $productToAdd = $product;
            }



            // Kiểm tra inventory trước khi add - chặn hoàn toàn nếu hết hàng
            if ($productToAdd->manage_stock && $productToAdd->totalQuantity() < $request->quantity) {
                $availableQty = $productToAdd->totalQuantity();
                return response()->json([
                    'success' => false,
                    'message' => "Số lượng không đủ. Chỉ còn {$availableQty} sản phẩm trong kho. Vui lòng liên hệ hotline để biết thêm chi tiết.",
                    'toast' => Toast::error("Số lượng không đủ. Chỉ còn {$availableQty} sản phẩm trong kho.")
                ]);
            }

            $cartData = [
                'product_id' => $productToAdd->id,
                'quantity' => $request->quantity,
            ];

            if ($request->has('unit_type')) {
                $cartData['unit_type'] = $request->unit_type;
            }

            $cart = Cart::addProduct($productToAdd, $cartData);

            return response()->json([
                'success' => true,
                'message' => 'Đã thêm sản phẩm vào giỏ hàng',
                'cart_count' => $cart->items->count(),
                'toast' => Toast::success('Đã thêm sản phẩm vào giỏ hàng')
            ]);
        } catch (\Exception $e) {

            return response()->json([
                'success' => false,
                'message' => 'Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại sau.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ],
                'toast' => Toast::error('Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại sau.')
            ]);
        }
    }

    /**
     * Xóa sản phẩm khỏi giỏ hàng
     */
    public function removeItem(Request $request)
    {
        try {
            $itemId = $request->input('item_id');

            // Kiểm tra item có tồn tại không
            $cartItem = CartItem::find($itemId);
            if (!$cartItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm trong giỏ hàng'
                ]);
            }

            // Kiểm tra quyền xóa
            if ($cartItem->cart->customer_id !== auth()->guard('customer')->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền xóa sản phẩm này'
                ]);
            }

            // Lấy cart để cập nhật tổng tiền
            $cart = $cartItem->cart;

            // Xóa item
            $cartItem->delete();

            // Cập nhật lại tổng tiền của cart
            $cart->sub_total = $cart->items->sum('total');
            $cart->base_sub_total = $cart->items->sum('base_total');
            $cart->grand_total = $cart->sub_total - $cart->discount_amount;
            $cart->base_grand_total = $cart->base_sub_total - $cart->base_discount_amount;
            $cart->save();

            // Cập nhật số lượng sản phẩm trong giỏ hàng
            $cartCount = $cart->items->count();

            return response()->json([
                'success' => true,
                'message' => 'Xóa sản phẩm thành công',
                'cart_count' => $cartCount,
                'toast' => [
                    'type' => 'success',
                    'message' => 'Xóa sản phẩm thành công'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa sản phẩm',
                'toast' => [
                    'type' => 'error',
                    'message' => 'Có lỗi xảy ra khi xóa sản phẩm'
                ]
            ]);
        }
    }

    /**
     * Xóa nhiều sản phẩm khỏi giỏ hàng
     */
    public function removeItems(Request $request)
    {
        try {
            $itemIds = $request->input('item_ids');

            if (empty($itemIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không có sản phẩm nào được chọn'
                ]);
            }

            // Lấy cart hiện tại
            $cart = Cart::getCart();

            // Kiểm tra quyền xóa
            $cartItems = CartItem::whereIn('id', $itemIds)
                ->where('cart_id', $cart->id)
                ->get();

            if ($cartItems->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy sản phẩm nào trong giỏ hàng'
                ]);
            }

            // Xóa các items
            $cartItems->each->delete();

            // Cập nhật lại tổng tiền của cart
            $cart->sub_total = $cart->items->sum('total');
            $cart->base_sub_total = $cart->items->sum('base_total');
            $cart->grand_total = $cart->sub_total - $cart->discount_amount;
            $cart->base_grand_total = $cart->base_sub_total - $cart->base_discount_amount;
            $cart->save();

            // Cập nhật số lượng sản phẩm trong giỏ hàng
            $cartCount = $cart->items->count();

            return response()->json([
                'success' => true,
                'message' => 'Xóa sản phẩm thành công',
                'cart_count' => $cartCount,
                'toast' => [
                    'type' => 'success',
                    'message' => 'Xóa sản phẩm thành công'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa sản phẩm',
                'toast' => [
                    'type' => 'error',
                    'message' => 'Có lỗi xảy ra khi xóa sản phẩm'
                ]
            ]);
        }
    }

    /**
     * Add to cart - alias for store method
     */
    public function addToCart(Request $request): JsonResponse
    {
        return $this->store($request);
    }

    public function checkout(Request $request)
    {
        // Lấy danh sách ID sản phẩm được chọn
        $selectedItems = json_decode($request->input('selected_items', '[]'), true);

        if (empty($selectedItems)) {
            return redirect()->back()->with('error', 'Vui lòng chọn ít nhất một sản phẩm');
        }

        // Lưu vào session
        session(['checkout_items' => $selectedItems]);

        // Chuyển hướng đến trang checkout
        return redirect()->route('checkout');
    }
}
