<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/profile.css']); ?>
<?php echo $__env->make('medical::common.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://npmcdn.com/flatpickr/dist/l10n/vn.js"></script>

<style>
    .flatpickr-calendar.medical-theme {
        border: 1px solid #FF6B00;
    }
    .flatpickr-calendar.medical-theme .flatpickr-months {
        background-color: #FF6B00;
        color: white;
    }
    .flatpickr-calendar.medical-theme .flatpickr-month {
        color: white;
    }
    .flatpickr-calendar.medical-theme .flatpickr-weekday {
        color: #FF6B00;
    }
    .flatpickr-calendar.medical-theme .flatpickr-day.selected {
        background: #FF6B00;
        border-color: #FF6B00;
    }
    .flatpickr-calendar.medical-theme .flatpickr-day:hover {
        background: rgba(255, 107, 0, 0.2);
    }
    .flatpickr-calendar.medical-theme .flatpickr-day.today {
        border-color: #FF6B00;
    }
    .flatpickr-calendar.medical-theme .flatpickr-current-month .flatpickr-monthDropdown-months:hover,
    .flatpickr-calendar.medical-theme .numInputWrapper:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    .flatpickr-calendar.medical-theme .flatpickr-monthDropdown-month {
        background-color: #FF6B00;
    }
</style>


<div class="container-fluid">
    <div class="row profile-container">
        <!-- Sidebar Tabs -->
        <div class="col-12 col-lg-3 profile-sidebar">
            <div class="profile-user-box">
                <div class="profile-avatar">
                    <i class="fa fa-user-circle" style="font-size:48px;color:#bbb"></i>
                </div>
                <div class="profile-user-info">
                    <div class="profile-user-name">Hello! <?php echo e(trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? '')) ?: $user->name); ?></div>
                    <div class="profile-user-email"><?php echo e($user->email); ?></div>
                </div>
            </div>
            <div class="profile-tabs">
                <div class="profile-tab" data-tab="customer">
                    <i class="fa fa-user"></i> Thông tin cá nhân
                </div>
                <div class="profile-tab" data-tab="cost">
                    <i class="fa fa-file-invoice-dollar"></i> Báo giá của tôi
                </div>
                <div class="profile-tab" data-tab="orders">
                    <i class="fa fa-clipboard-list"></i> Đơn hàng của tôi
                </div>
                <div class="profile-tab" data-tab="address">
                    <i class="fa-solid fa-location-dot"></i> Địa chỉ của tôi
                </div>
            </div>
        </div>
        <!-- Main Content -->
        <div class="col-12 col-lg-9 profile-main">
        <!-- Tab: Thông tin cá nhân -->
        <div class="profile-tab-content" id="tab-customer">
            <div class="profile-header">
                <h2>Thông tin cá nhân</h2>
                <button type="button" class="profile-edit-btn" id="edit-btn">Chỉnh sửa</button>
            </div>
            <?php if(session('error')): ?>
                <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
            <?php endif; ?>
            <?php if(session('success')): ?>
                <div class="alert alert-success"><?php echo e(session('success')); ?></div>
            <?php endif; ?>
            <form id="profile-form" action="<?php echo e(route('profile.update')); ?>" method="post">
                <?php echo csrf_field(); ?>
                <table class="profile-info-table">
                    <tr>
                        <td>Họ và tên</td>
                        <td>
                            <input class="input-text" type="text" name="fullname" value="<?php echo e(trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''))); ?>" disabled>
                        </td>
                    </tr>

                    <tr>
                        <td>Giới tính</td>
                        <td>
                            <div style="display: flex; gap: 16px; align-items: center;padding-left: 4px;">
                                <label style="display: flex; align-items: center; gap: 4px; margin-bottom: 0;">
                                    <input type="radio" name="gender" value="male" <?php echo e($user->gender == 'male' ? 'checked' : ''); ?> disabled> Nam
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px; margin-bottom: 0;">
                                    <input type="radio" name="gender" value="female" <?php echo e($user->gender == 'female' ? 'checked' : ''); ?> disabled> Nữ
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Ngày sinh</td>
                        <td>
                            <input class="input-text flatpickr-input" type="text" id="dob" name="dob" placeholder="dd/mm/yyyy" value="<?php echo e($user->date_of_birth ? date('d/m/Y', strtotime($user->date_of_birth)) : ''); ?>" autocomplete="off" disabled>
                        </td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>
                            <input class="input-text" type="email" name="email" value="<?php echo e($user->email); ?>" disabled>
                        </td>
                    </tr>
                    <tr>
                        <td>Địa chỉ</td>
                        <td>
                            <input class="input-text" type="text" name="address"
                                value="<?php echo e(optional($user->addresses->firstWhere('default_address', 1))->address ?? '-'); ?>" disabled>
                        </td>
                    </tr>
                </table>
                <button style="width: 25%; display: none;" type="submit" class="profile-delete-btn" id="save-btn">Lưu thông tin</button>
            </form>
        </div>
        <!-- Tab: Báo giá của tôi -->
        <div class="profile-tab-content" id="tab-cost">
            <div class="order-table-wrapper">


                <div class="quote-filter-container">
                    <label for="quote-sort">Thứ tự:</label>
                    <select id="quote-sort" class="quote-sort-dropdown">
                        <option value="id-asc">Mã báo giá tăng dần</option>
                        <option value="date-desc">Ngày tạo mới nhất</option>
                        <option value="date-asc">Ngày tạo cũ nhất</option>
                        <option value="quantity-asc">Số lượng sản phẩm tăng dần</option>
                        <option value="quantity-desc">Số lượng sản phẩm giảm dần</option>
                    </select>
                </div>
                <table class="order-table">
                    <thead>
                        <tr>
                            <th>Mã báo giá</th>
                            <th>Số lượng</th>
                            <th>Sản phẩm</th>
                            <th>Trạng thái</th>
                            <th>Ngày gửi yêu cầu báo giá</th>
                            <th style="text-align: center; vertical-align: middle;">Chi tiết</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $quotes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quote): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>#<?php echo e($quote->id); ?></td>
                                <td class="center-align"><?php echo e($quote->items->count()); ?></td>
                                <td class="product-thumbnails">
                                    <div class="product-images-container">
                                        <?php $__currentLoopData = $quote->items->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <img src="<?php echo e($item->product && $item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                                                 alt="<?php echo e($item->name); ?>"
                                                 title="<?php echo e($item->name); ?>"
                                                 class="product-thumbnail">
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                    <?php if($quote->items->count() > 2): ?>
                                        <div class="more-items-text">và <?php echo e($quote->items->count() - 2); ?> sản phẩm khác</div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                        $statusClass = 'status-pending';
                                        $statusText = 'Đang xử lý';

                                        switch($quote->status) {
                                            case 'pending':
                                                $statusClass = 'status-pending';
                                                $statusText = 'Đang xử lý';
                                                break;
                                            case 'processing':
                                                $statusClass = 'status-processing';
                                                $statusText = 'Đang xử lý';
                                                break;
                                            case 'completed':
                                                $statusClass = 'status-completed';
                                                $statusText = 'Hoàn thành';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'status-cancelled';
                                                $statusText = 'Đã hủy';
                                                break;
                                            case 'approved':
                                                $statusClass = 'status-approved';
                                                $statusText = 'Đã duyệt';
                                                break;
                                            case 'rejected':
                                                $statusClass = 'status-rejected';
                                                $statusText = 'Từ chối';
                                                break;
                                            default:
                                                $statusClass = 'status-pending';
                                                $statusText = ucfirst($quote->status);
                                        }
                                    ?>
                                    <span class="quote-status <?php echo e($statusClass); ?>">
                                        <?php echo e($statusText); ?>

                                    </span>
                                </td>
                                <td><?php echo e($quote->updated_at->format('d/m/Y H:i')); ?></td>
                                <td style="text-align: center; vertical-align: middle;">
                                    <a href="/profile/cost_detail/<?php echo e($quote->id); ?>">
                                        <i class="fa-solid fa-eye fa-2x" style="color: #FF6B00; cursor: pointer;"></i>
                                    </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6">Bạn chưa có đơn báo giá nào.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            <div class="ingredient-pagination">
                    <?php echo e($quotes->links('vendor.pagination.custom')); ?>

                </div>
            </div>
        </div>
        <!-- Tab: Đơn hàng của tôi -->
        <div class="profile-tab-content" id="tab-orders">
            <div class="order-table-wrapper">
                <div class="quote-filter-container">
                    <label for="order-sort">Thứ tự:</label>
                    <select id="order-sort" class="quote-sort-dropdown">
                        <option value="id-asc">Mã đơn hàng tăng dần</option>
                        <option value="date-desc">Ngày đặt mới nhất</option>
                        <option value="date-asc">Ngày đặt cũ nhất</option>
                        <option value="quantity-asc">Số lượng sản phẩm tăng dần</option>
                        <option value="quantity-desc">Số lượng sản phẩm giảm dần</option>
                    </select>
                </div>
                <table class="order-table">
                    <thead>
                        <tr>
                            <th>Mã đơn hàng</th>
                            <th>Tổng tiền</th>
                            <th>Số lượng</th>
                            <th>Sản phẩm</th>
                            <th>Email/SĐT/Địa chỉ</th>
                            <th>Trạng thái</th>
                            <th>Ngày thanh toán</th>
                            <th>Ngày đặt hàng</th>
                            <th style="text-align: center; vertical-align: middle;">Chi tiết</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>#<?php echo e($order->increment_id); ?></td>
                            <td><?php echo e(number_format($order->grand_total)); ?>đ</td>
                            <td class="center-align"><?php echo e($order->items->count()); ?></td>
                            <td class="product-thumbnails">
                                <div class="product-images-container">
                                    <?php $__currentLoopData = $order->items->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <img src="<?php echo e($item->product && $item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                                             alt="<?php echo e($item->name); ?>"
                                             title="<?php echo e($item->name); ?>"
                                             class="product-thumbnail">
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php if($order->items->count() > 2): ?>
                                    <div class="more-items-text">và <?php echo e($order->items->count() - 2); ?> sản phẩm khác</div>
                                <?php endif; ?>
                            </td>
                            <td class="contact-info">
                                <div><?php echo e($user->email); ?></div>
                                <div><?php echo e($user->phone ?? '-'); ?></div>
                                <div><?php echo e($order->shipping_address->address ?? '-'); ?></div>
                            </td>
                            <td>
                                <span class="order-status status-pending">
                                    Đang xử lý
                                </span>
                            </td>
                            <td style="text-align: center;"><?php echo e($order->paid_at ? date('d/m/Y H:i', strtotime($order->paid_at)) : '-'); ?></td>
                            <td style="text-align: center;"><?php echo e($order->created_at->format('d/m/Y H:i')); ?></td>
                            <td style="text-align: center; vertical-align: middle;">
                                <a href="/order_detail/<?php echo e($order->id); ?>">
                                    <i class="fa-solid fa-eye fa-2x" style="color: #FF6B00; cursor: pointer;"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="10">Bạn chưa có đơn hàng nào.</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <div class="ingredient-pagination">
                    <?php echo e($orders->links('vendor.pagination.custom')); ?>

                </div>
            </div>
        </div>

        <div class="profile-tab-content" id="tab-address">
            <div class="address-header">
                <span class="address-title">Số địa chỉ nhận hàng</span>
                <button class="address-add-btn"><i class="fa fa-plus"></i> Thêm địa chỉ</button>
            </div>
            <div class="address-list">
                <?php $__currentLoopData = $user->addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="address-item">
                    <input type="number" name="addressId" value="<?php echo e($address->id); ?>" hidden>
                    <div class="address-info">
                        <span class="address-name"><?php echo e($address->name); ?></span>
                        <span class="address-phone">| <?php echo e($address->phone); ?></span>
                    </div>
                    <div class="address-actions">
                        <button class="address-update" data-id="<?php echo e($address->id); ?>"><i class="fa fa-edit"></i></button>
                        <button class="address-delete" data-id="<?php echo e($address->id); ?>"><i class="fa fa-trash"></i></button>
                    </div>
                    <div class="address-detail">
                        <?php echo e(is_array($address->address) ? implode(', ', $address->address) : $address->address); ?>

                    </div>
                    <?php if($address->default_address): ?>
                    <span class="address-type" style="font-weight: bold;">Địa chỉ mặc định</span>
                    <?php endif; ?>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Modal Địa chỉ -->
<div id="address-modal" class="address-modal" style="display:none;">
    <div class="address-modal-overlay"></div>
    <div class="address-modal-content">
        <div class="address-modal-header">
            <span id="address-modal-title">Cập nhật địa chỉ</span>
            <button class="address-modal-close">&times;</button>
        </div>
        <form id="address-modal-form" action="<?php echo e(route('checkout.create-new-address')); ?>" method="POST">
            <input type="number" id="address-id" name="address-id" value="" hidden>
            <div class="address-modal-group">
                <label>Họ và tên</label>
                <input type="text" id="modal-name" name="name" required>
            </div>
            <div class="address-modal-group">
                <label>Số điện thoại</label>
                <input type="text" id="modal-phone" name="phone" required>
            </div>
            <div class="address-modal-group">
                <label>Địa chỉ</label>
                <textarea id="modal-detail" name="address" required></textarea>
            </div>
            <div class="address-modal-group" style="margin-bottom: 18px;">
                <label>
                    <input type="checkbox" class="default" id="modal-default" name="default">
                    Đặt làm địa chỉ mặc định
                </label>
            </div>
            <div class="address-modal-actions">
                <button type="button" class="address-modal-cancel">Quay lại</button>
                <button type="button" class="address-modal-save" onclick="saveEditAddress()">Lưu lại</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Xác nhận thoát -->
<div id="confirm-exit-modal" class="address-modal" style="display:none;">
    <div class="address-modal-overlay"></div>
    <div class="address-modal-content" style="max-width: 400px; text-align: center;">
        <div class="address-modal-header" style="justify-content: center; border-bottom: none; padding-bottom: 0;">
            <span id="confirm-exit-title" style="font-size: 18px; font-weight: 600;">Bạn có chắc muốn thoát?</span>
        </div>
        <div style="padding: 20px 20px 10px;">
            <p>Bạn đang chỉnh sửa thông tin cá nhân. Bạn có chắc muốn rời đi mà không lưu thông tin?</p>
        </div>
        <div class="address-modal-actions" style="justify-content: center; padding: 10px 20px 20px;">
            <button type="button" id="confirm-exit-cancel" style="background: #f5f5f5; color: #444; border: 1px solid #FF6B00; border-radius: 6px; padding: 8px 18px; font-size: 1rem; cursor: pointer; font-weight: 500; margin-right: 10px;">Ở lại</button>
            <button type="button" id="confirm-exit-confirm" style="background-color: #FF6B00; color: white; border: none; border-radius: 6px; padding: 8px 18px; font-size: 1rem; cursor: pointer; font-weight: 500;">Thoát</button>
        </div>
    </div>
</div>

<!-- Modal Thông báo thành công -->
<div id="success-modal" class="address-modal" style="display:none;">
    <div class="address-modal-overlay"></div>
    <div class="address-modal-content" style="max-width: 400px; text-align: center;">
        <div style="padding: 30px 20px 20px;">
            <p style="font-size: 18px; font-weight: 600; margin-bottom: 20px;">Cập nhật thông tin thành công</p>
            <button type="button" id="success-ok" style="min-width: 100px; background-color: #FF6B00; color: white; border: none; border-radius: 6px; padding: 8px 18px; font-size: 1rem; cursor: pointer; font-weight: 500;">OK</button>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // Xử lý Edit và Save
        const editBtn = document.getElementById('edit-btn');
        const saveBtn = document.getElementById('save-btn');
        const form = document.getElementById('profile-form');
        const inputs = form.querySelectorAll('input');
        let isEditing = false; // Biến để theo dõi trạng thái đang chỉnh sửa
        let originalValues = {}; // Lưu giá trị gốc

        // Hàm lưu giá trị gốc
        function saveOriginalValues() {
            inputs.forEach(input => {
                if (input.type === 'radio') {
                    originalValues[input.name] = document.querySelector(`input[name="${input.name}"]:checked`)?.value || '';
                } else {
                    originalValues[input.name] = input.value;
                }
            });
        }

        // Hàm khôi phục giá trị gốc
        function restoreOriginalValues() {
            inputs.forEach(input => {
                if (input.type === 'radio') {
                    if (originalValues[input.name] && input.value === originalValues[input.name]) {
                        input.checked = true;
                    }
                } else {
                    input.value = originalValues[input.name] || '';
                }
            });
        }

        // Lấy tab từ URL
        let path = window.location.pathname.split('/');
        let tab = path[2] || 'customer';

        // Kích hoạt tab sidebar
        document.querySelectorAll('.profile-tab').forEach(function(el) {
            el.classList.remove('active');
            if (el.dataset.tab === tab) el.classList.add('active');
        });

        // Ẩn tất cả tab, chỉ hiện tab đúng
        function showActiveTab(tab) {
            document.querySelectorAll('.profile-tab-content').forEach(function(el) {
                el.classList.remove('active');
            });
            let activeContent = document.getElementById('tab-' + tab);
            if (activeContent) activeContent.classList.add('active');
        }
        showActiveTab(tab);

        // Xử lý modal xác nhận thoát
        const confirmExitModal = document.getElementById('confirm-exit-modal');
        const confirmExitCancel = document.getElementById('confirm-exit-cancel');
        const confirmExitConfirm = document.getElementById('confirm-exit-confirm');
        const successModal = document.getElementById('success-modal');
        const successOk = document.getElementById('success-ok');

        // Đóng modal khi click vào overlay
        document.querySelectorAll('.address-modal-overlay').forEach(function(overlay) {
            overlay.addEventListener('click', function() {
                this.parentElement.style.display = 'none';
            });
        });

        // Biến để lưu trữ thông tin tab đích khi chuyển tab
        let targetTab = null;

        // Hiển thị modal xác nhận thoát
        function showConfirmExitModal(callback) {
            confirmExitModal.style.display = 'flex';

            // Xử lý nút Ở lại
            confirmExitCancel.onclick = function() {
                confirmExitModal.style.display = 'none';
            };

            // Xử lý nút Thoát
            confirmExitConfirm.onclick = function() {
                confirmExitModal.style.display = 'none';
                if (typeof callback === 'function') {
                    callback();
                }
            };
        }

        // Hiển thị modal thành công
        function showSuccessModal(message) {
            const messageElement = successModal.querySelector('p');
            messageElement.textContent = message;
            successModal.style.display = 'flex';

            successOk.onclick = function() {
                successModal.style.display = 'none';
            };
        }

        // Click tab để chuyển tab (không reload)
        document.querySelectorAll('.profile-tab').forEach(function(el) {
            el.addEventListener('click', function() {
                // Lưu tab đích
                targetTab = el.dataset.tab;

                // Kiểm tra nếu đang chỉnh sửa thì hiển thị thông báo
                if (isEditing) {
                    showConfirmExitModal(function() {
                        // Nếu người dùng xác nhận thoát
                        window.history.pushState({}, '', '/profile/' + targetTab);
                        document.querySelectorAll('.profile-tab').forEach(e => e.classList.remove('active'));
                        el.classList.add('active');
                        showActiveTab(targetTab);

                        // Reset trạng thái chỉnh sửa và khôi phục giá trị gốc
                        restoreOriginalValues();
                        inputs.forEach(input => input.disabled = true);
                        saveBtn.style.display = 'none';
                        editBtn.disabled = false;
                        editBtn.innerText = 'Chỉnh sửa';
                        isEditing = false;

                        // Hủy flatpickr khi thoát chỉnh sửa
                        if (flatpickrInstance) {
                            flatpickrInstance.destroy();
                            flatpickrInstance = null;
                        }
                    });
                } else {
                    // Nếu không đang chỉnh sửa, chuyển tab bình thường
                    window.history.pushState({}, '', '/profile/' + targetTab);
                    document.querySelectorAll('.profile-tab').forEach(e => e.classList.remove('active'));
                    el.classList.add('active');
                    showActiveTab(targetTab);
                }
            });
        });

        // Hỗ trợ back/forward trên trình duyệt
        window.addEventListener('popstate', function() {
            // Kiểm tra nếu đang chỉnh sửa thì hiển thị thông báo
            if (isEditing) {
                showConfirmExitModal(function() {
                    // Nếu người dùng xác nhận thoát
                    let path = window.location.pathname.split('/');
                    let tab = path[2] || 'customer';
                    document.querySelectorAll('.profile-tab').forEach(function(el) {
                        el.classList.remove('active');
                        if (el.dataset.tab === tab) el.classList.add('active');
                    });
                    showActiveTab(tab);

                    // Reset trạng thái chỉnh sửa và khôi phục giá trị gốc
                    restoreOriginalValues();
                    inputs.forEach(input => input.disabled = true);
                    saveBtn.style.display = 'none';
                    editBtn.disabled = false;
                    editBtn.innerText = 'Chỉnh sửa';
                    isEditing = false;

                    // Hủy flatpickr khi thoát chỉnh sửa
                    if (flatpickrInstance) {
                        flatpickrInstance.destroy();
                        flatpickrInstance = null;
                    }
                });

                // Ngăn chặn sự kiện popstate mặc định
                history.pushState(null, null, window.location.href);
            } else {
                // Nếu không đang chỉnh sửa, xử lý bình thường
                let path = window.location.pathname.split('/');
                let tab = path[2] || 'customer';
                document.querySelectorAll('.profile-tab').forEach(function(el) {
                    el.classList.remove('active');
                    if (el.dataset.tab === tab) el.classList.add('active');
                });
                showActiveTab(tab);
            }
        });

        // Thêm sự kiện beforeunload để cảnh báo khi người dùng rời trang
        window.addEventListener('beforeunload', function(e) {
            if (isEditing) {
                e.preventDefault();
                e.returnValue = 'Bạn đang chỉnh sửa thông tin cá nhân. Bạn có chắc muốn rời đi mà không lưu thông tin?';
                return e.returnValue;
            }
        });



        // Khởi tạo Flatpickr cho trường ngày sinh
        const dateField = document.getElementById('dob');
        let flatpickrInstance = null;

        editBtn.addEventListener('click', function() {
            // Lưu giá trị gốc trước khi cho phép chỉnh sửa
            saveOriginalValues();

            inputs.forEach(input => input.disabled = false);
            saveBtn.style.display = 'block';
            editBtn.disabled = true;
            editBtn.innerText = 'Đang chỉnh sửa...';
            isEditing = true; // Đánh dấu đang trong trạng thái chỉnh sửa

            // Khởi tạo flatpickr khi bắt đầu chỉnh sửa
            if (dateField) {
                flatpickrInstance = flatpickr(dateField, {
                    dateFormat: "d/m/Y",
                    allowInput: true,
                    disableMobile: true,
                    altInput: true,
                    altFormat: "d/m/Y",
                    locale: {
                        firstDayOfWeek: 1, // Thứ 2 là ngày đầu tuần
                        weekdays: {
                            shorthand: ["CN", "T2", "T3", "T4", "T5", "T6", "T7"],
                            longhand: ["Chủ Nhật", "Thứ Hai", "Thứ Ba", "Thứ Tư", "Thứ Năm", "Thứ Sáu", "Thứ Bảy"]
                        },
                        months: {
                            shorthand: ["Th1", "Th2", "Th3", "Th4", "Th5", "Th6", "Th7", "Th8", "Th9", "Th10", "Th11", "Th12"],
                            longhand: ["Tháng Một", "Tháng Hai", "Tháng Ba", "Tháng Tư", "Tháng Năm", "Tháng Sáu", "Tháng Bảy", "Tháng Tám", "Tháng Chín", "Tháng Mười", "Tháng Mười Một", "Tháng Mười Hai"]
                        }
                    },
                    // Tùy chỉnh theme
                    monthSelectorType: "static",
                    yearSelectorType: "dropdown",
                    showMonths: 1,
                    time_24hr: true,
                    // Cho phép chọn năm trong khoảng rộng (1900-2023)
                    minDate: "01/01/1900",
                    maxDate: new Date().toISOString().split('T')[0],
                    // Đảm bảo ngày được định dạng đúng khi submit
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                            const formattedDate = selectedDates[0].toLocaleDateString('vi-VN', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric'
                            }).replace(/\./g, '/');
                            dateField.value = formattedDate;
                        }
                    },
                    // Tùy chỉnh màu sắc
                    onReady: function(selectedDates, dateStr, instance) {
                        // Thêm class cho calendar để tùy chỉnh màu sắc
                        setTimeout(function() {
                            const calendar = document.querySelector('.flatpickr-calendar');
                            if (calendar) {
                                calendar.classList.add('medical-theme');
                            }

                            // Tạo dropdown năm với nhiều lựa chọn hơn
                            const yearInput = document.querySelector('.numInput.cur-year');
                            if (yearInput) {
                                // Thay đổi sự kiện khi click vào năm
                                yearInput.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const currentYear = new Date().getFullYear();
                                    const yearDropdown = document.createElement('select');
                                    yearDropdown.className = 'flatpickr-year-dropdown';
                                    yearDropdown.style.position = 'absolute';
                                    yearDropdown.style.top = '100%';
                                    yearDropdown.style.left = '0';
                                    yearDropdown.style.zIndex = '1000';
                                    yearDropdown.style.height = '200px';
                                    yearDropdown.style.overflow = 'auto';
                                    yearDropdown.style.backgroundColor = 'white';
                                    yearDropdown.style.border = '1px solid #ddd';
                                    yearDropdown.style.borderRadius = '4px';
                                    yearDropdown.style.padding = '5px';

                                    // Thêm các năm từ 1900 đến hiện tại
                                    for (let year = 1900; year <= currentYear; year++) {
                                        const option = document.createElement('option');
                                        option.value = year;
                                        option.textContent = year;
                                        if (year === instance.currentYear) {
                                            option.selected = true;
                                        }
                                        yearDropdown.appendChild(option);
                                    }

                                    // Xử lý khi chọn năm
                                    yearDropdown.addEventListener('change', function() {
                                        instance.currentYear = parseInt(this.value);
                                        instance.redraw();
                                        this.remove();
                                    });

                                    // Thêm dropdown vào DOM
                                    yearInput.parentNode.appendChild(yearDropdown);

                                    // Đóng dropdown khi click ra ngoài
                                    document.addEventListener('click', function closeDropdown(e) {
                                        if (!yearDropdown.contains(e.target) && e.target !== yearInput) {
                                            yearDropdown.remove();
                                            document.removeEventListener('click', closeDropdown);
                                        }
                                    });
                                });
                            }
                        }, 100);
                    }
                });
            }
        });

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            // Gửi form bằng AJAX
            fetch(form.action, {
                    method: 'POST',
                    body: new FormData(form)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Cập nhật tên hiển thị trong sidebar
                        const fullnameInput = form.querySelector('input[name="fullname"]');
                        const profileUserName = document.querySelector('.profile-user-name');
                        if (fullnameInput && profileUserName) {
                            const newName = fullnameInput.value.trim();
                            if (newName) {
                                profileUserName.textContent = 'Hello! ' + newName;
                            }
                        }

                        // Nếu thành công thì disable input và reset form
                        inputs.forEach(input => input.disabled = true);
                        saveBtn.style.display = 'none';
                        editBtn.disabled = false;
                        editBtn.innerText = 'Chỉnh sửa';
                        isEditing = false; // Đánh dấu đã hoàn thành chỉnh sửa

                        // Hủy flatpickr khi hoàn thành chỉnh sửa
                        if (flatpickrInstance) {
                            flatpickrInstance.destroy();
                            flatpickrInstance = null;
                        }

                        showSuccessModal('Cập nhật thông tin thành công');
                    } else {
                        // Hiển thị tất cả lỗi validation
                        if (data.errors && Array.isArray(data.errors)) {
                            let errorMessage;
                            if (data.errors.length === 1) {
                                // Nếu chỉ có 1 lỗi, hiển thị trực tiếp
                                errorMessage = data.errors[0];
                            } else {
                                // Nếu có nhiều lỗi, rút gọn thành danh sách tên trường
                                const fieldNames = data.errors.map(error => {
                                    // Trích xuất tên trường từ message lỗi
                                    if (error.includes('họ và tên')) return 'họ tên';
                                    if (error.includes('email')) return 'email';
                                    if (error.includes('địa chỉ')) return 'địa chỉ';
                                    if (error.includes('ngày sinh')) return 'ngày sinh';
                                    if (error.includes('giới tính')) return 'giới tính';
                                    // Fallback: trả về message gốc nếu không match
                                    return error;
                                });

                                errorMessage = 'Vui lòng nhập ' + fieldNames.join(', ');
                            }
                            ModalCommon.showError(errorMessage);
                        } else {
                            // Fallback cho trường hợp không có errors array
                            ModalCommon.showError(data.message || 'Có lỗi xảy ra, vui lòng thử lại.');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    ModalCommon.showError(error.message);
                });
        });
    });

    // Kích hoạt nút phân trang khi click
    document.querySelectorAll('.order-pagination').forEach(function(pagination) {
        pagination.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                // Bỏ active ở tất cả nút trong cùng phân trang
                pagination.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                // Thêm active cho nút vừa bấm
                e.target.classList.add('active');
            }
        });
    });

    // Xử lý sắp xếp báo giá và đơn hàng
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý sắp xếp báo giá
        const quoteSortDropdown = document.getElementById('quote-sort');
        if (quoteSortDropdown) {
            quoteSortDropdown.addEventListener('change', function() {
                const sortValue = quoteSortDropdown.value;
                // Redirect với sort parameter và reset page về 1
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('quote_sort', sortValue);
                currentUrl.searchParams.set('page', '1'); // Reset page về 1
                window.location.href = currentUrl.toString();
            });
        }

        // Xử lý sắp xếp đơn hàng
        const orderSortDropdown = document.getElementById('order-sort');
        if (orderSortDropdown) {
            orderSortDropdown.addEventListener('change', function() {
                const sortValue = orderSortDropdown.value;
                // Redirect với sort parameter và reset page về 1
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('order_sort', sortValue);
                currentUrl.searchParams.set('page', '1'); // Reset page về 1
                window.location.href = currentUrl.toString();
            });
        }

        // Set giá trị mặc định cho dropdown dựa trên URL params
        const urlParams = new URLSearchParams(window.location.search);
        const quoteSort = urlParams.get('quote_sort');
        const orderSort = urlParams.get('order_sort');
        
        if (quoteSort && quoteSortDropdown) {
            quoteSortDropdown.value = quoteSort;
        }
        
        if (orderSort && orderSortDropdown) {
            orderSortDropdown.value = orderSort;
        }
    });

    // Xử lý xóa địa chỉ với modal xác nhận
    document.querySelectorAll('.address-delete').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const addressId = btn.getAttribute('data-id');
            ModalCommon.showConfirm('Bạn có chắc muốn xóa địa chỉ này?', function() {
                fetch("<?php echo e(route('checkout.delete-address')); ?>", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({ id: addressId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Toastify({
                            text: data.message || 'Xóa địa chỉ thành công!',
                            duration: 3000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#4CAF50",
                            close: true
                        }).showToast();
                        // Xóa khỏi DOM
                        btn.closest('.address-item').remove();
                    } else {
                        Toastify({
                            text: data.message || 'Xóa địa chỉ thất bại!',
                            duration: 3000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                            close: true
                        }).showToast();
                    }
                })
                .catch(error => {
                    Toastify({
                        text: error.message || 'Có lỗi xảy ra',
                        duration: 3000,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                        close: true
                    }).showToast();
                });
            });
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('address-modal');
        const modalTitle = document.getElementById('address-modal-title');
        const addressId = document.getElementById('address-id');
        const modalName = document.getElementById('modal-name');
        const modalPhone = document.getElementById('modal-phone');
        const modalDetail = document.getElementById('modal-detail');
        const modalDefault = document.getElementById('modal-default');
        const modalForm = document.getElementById('address-modal-form');
        const closeModal = () => {
            modal.style.display = 'none';
        };

        // Mở modal cập nhật
        document.querySelectorAll('.address-update').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                modalTitle.textContent = 'Cập nhật địa chỉ';
                // Lấy thông tin từ address-item gần nhất
                const item = btn.closest('.address-item');
                addressId.value = item.querySelector('input[name="addressId"]').value;
                modalName.value = item.querySelector('.address-name').textContent.trim();
                modalPhone.value = item.querySelector('.address-phone').textContent.replace('|', '').trim();
                modalDetail.value = item.querySelector('.address-detail').textContent.trim();
                // Nếu có class/mã nhận biết địa chỉ mặc định thì check, ví dụ:
                modalDefault.checked = !!item.querySelector('.address-type') && item.querySelector('.address-type').textContent.includes('mặc định');
                modal.style.display = 'flex';
            });
        });

        // Mở modal thêm mới
        document.querySelector('.address-add-btn').addEventListener('click', function(e) {
            e.preventDefault();
            modalTitle.textContent = 'Địa chỉ mới';
            modalName.value = '';
            modalPhone.value = '';
            modalDetail.value = '';
            modalDefault.checked = false;
            modal.style.display = 'flex';
        });

        // Đóng modal
        modal.querySelector('.address-modal-close').onclick =
            modal.querySelector('.address-modal-cancel').onclick =
            modal.querySelector('.address-modal-overlay').onclick = closeModal;

        // Ngăn sự kiện nổi bọt khi click vào content
        modal.querySelector('.address-modal-content').onclick = function(e) {
            e.stopPropagation();
        };

        // Xử lý submit form (bạn có thể thay bằng ajax)
        modalForm.onsubmit = function(e) {
            e.preventDefault();
            // Xử lý lưu địa chỉ ở đây
            // Ví dụ: alert('Lưu địa chỉ: ' + modalName.value + ', ' + modalPhone.value + ', ' + modalDetail.value + ', default: ' + modalDefault.checked);
            closeModal();
        };
    });

    function saveEditAddress() {
        const modalForm = document.getElementById('address-modal-form');
        const modalName = document.getElementById('modal-name');
        const modalPhone = document.getElementById('modal-phone');
        const modalDetail = document.getElementById('modal-detail');
        
        // Xóa các thông báo lỗi cũ
        document.querySelectorAll('.address-modal-group .error-message').forEach(error => error.remove());
        document.querySelectorAll('.address-modal-group input, .address-modal-group textarea').forEach(field => {
            field.classList.remove('is-invalid');
        });
        
        let isValid = true;
        
        // Validate tên
        if (!modalName.value.trim()) {
            showFieldError(modalName, 'Vui lòng nhập họ và tên');
            isValid = false;
        }
        
        // Validate số điện thoại
        if (!modalPhone.value.trim()) {
            showFieldError(modalPhone, 'Vui lòng nhập số điện thoại');
            isValid = false;
        } else if (!/^[0-9]{10,11}$/.test(modalPhone.value.trim())) {
            showFieldError(modalPhone, 'Số điện thoại không hợp lệ');
            isValid = false;
        }
        
        // Validate địa chỉ
        if (!modalDetail.value.trim()) {
            showFieldError(modalDetail, 'Vui lòng nhập địa chỉ');
            isValid = false;
        }
        
        // Nếu validation thất bại, không gửi form
        if (!isValid) {
            return;
        }
        
        const formData = new FormData(modalForm);
        
        const data = {
            add_id: formData.get('address-id'),
            name: formData.get('name'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            default: formData.get('default') === 'on' ? 1 : 0,
            _token: '<?php echo e(csrf_token()); ?>'
        };

        fetch(modalForm.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Toastify({
                        text: data.message,
                        duration: 3000,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#4CAF50",
                        close: true
                    }).showToast();

                    // Reload trang để cập nhật danh sách địa chỉ
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            })
            .catch(error => {
                Toastify({
                    text: error.message || 'Có lỗi xảy ra',
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();
            });
    }

    // Hàm hiển thị lỗi cho từng field
    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        const errorMessage = document.createElement('span');
        errorMessage.className = 'error-message';
        errorMessage.textContent = message;
        errorMessage.style.color = '#dc3545';
        errorMessage.style.fontSize = '12px';
        errorMessage.style.marginTop = '4px';
        errorMessage.style.display = 'block';
        
        field.parentElement.appendChild(errorMessage);
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/profile/profile.blade.php ENDPATH**/ ?>