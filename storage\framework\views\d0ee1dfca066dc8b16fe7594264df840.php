<?php if($paginator->hasPages()): ?>
    <nav>
        <ul class="pagination">
            
            <?php if($paginator->onFirstPage()): ?>
                <button class="pagination-btn disabled" data-page="first" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">&laquo;</button>
            <?php else: ?>
                <button class="pagination-btn" data-page="first" onclick="window.location.href='<?php echo e($paginator->previousPageUrl()); ?>'" rel="prev" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">&laquo;</button>
            <?php endif; ?>

            
            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <?php if(is_string($element)): ?>
                    <button class="pagination-btn disabled" aria-disabled="true"><?php echo e($element); ?></button>
                <?php endif; ?>

                
                <?php if(is_array($element)): ?>
                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($page == $paginator->currentPage()): ?>
                            <button class="pagination-btn active" data-page="<?php echo e($page); ?>" aria-current="page"><?php echo e($page); ?></button>
                        <?php else: ?>
                            <button class="pagination-btn" data-page="<?php echo e($page); ?>" onclick="window.location.href='<?php echo e($url); ?>'"><?php echo e($page); ?></button>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
                <button class="pagination-btn" data-page="last" onclick="window.location.href='<?php echo e($paginator->nextPageUrl()); ?>'" rel="next" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">&raquo;</button>
            <?php else: ?>
                <button class="pagination-btn disabled" data-page="last" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">&raquo;</button>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>
<?php /**PATH /var/www/html/resources/themes/medical/views/pagination/default.blade.php ENDPATH**/ ?>