<?php

namespace Webkul\CartRule\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearCartRuleCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cart-rule:clear-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear cart rule condition attributes cache';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Cache::forget('cart_rule_condition_attributes');
        
        $this->info('Cart rule cache cleared successfully!');
        
        return 0;
    }
}
