<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/our_product.css']); ?>

<div class="our-product-page">

    <!-- CATEGORY/GENERAL PAGE HEADER -->
    <?php if(!$searchKeyword): ?>
    <div class="our-product-category-bar">
        <h2 class="our-product-title"><?php echo e($pageTitle ?? ($selectedCategory ? $selectedCategory->name : 'Thuố<PERSON>')); ?></h2>
        <?php if($selectedCategory && $selectedCategory->description): ?>
        <div class="category-description" style="color: #666; margin: 10px 0; font-size: 16px; line-height: 1.5;">
            <?php if($activeIngredients): ?>
            <div class="ingredient-list">
                <?php $__empty_1 = true; $__currentLoopData = $activeIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <a href="<?php echo e(route('ingredient_detail', $ingredient->slug)); ?>" class="ingredient-card">
                    <div class="ingredient-title"><?php echo e($ingredient->name); ?></div>
                </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="no-ingredients">
                    <p>Không tìm thấy hoạt chất nào.</p>
                </div>
                <?php endif; ?>
            </div>
            <?php echo $__env->make('medical::common.view_more', [
            'parentClass' => 'ingredient-list',
            'childClass' => 'ingredient-card',
            'perPage' => 8,
            'btnTextMore' => 'Xem thêm',
            'btnTextLess' => 'Thu nhỏ'
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        
        <?php if(!$selectedCategory && !request('featured') && !request('exclusive') && !in_array(request('sort'), ['best_seller', 'newest'])): ?>
        <div class="our-product-category-list">
            <?php if(!empty($categories_all)): ?>
            
            <?php $__currentLoopData = $categories_all; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
            $children = $category->children()->where('status', 1)->get();
            ?>
            <?php if($children->count()): ?>
            <?php $__currentLoopData = $children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
            $imagePath = $child->logo_path ?: '/images/product.png';
            ?>
            <div class="our-product-category-item" onclick="window.location.href='/product?category=<?php echo e($child->slug); ?>'">
                <img src="<?php echo e(asset('storage/' . $imagePath)); ?>" alt="">
                <span><?php echo e($child->name); ?></span>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        
        <?php if($selectedCategory && $data && $data->count() > 0): ?>
        <div class="our-product-category-list">
            <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
            $imagePath = $child->logo_path ?: '/images/product.png';
            ?>
            <div class="our-product-category-item" onclick="window.location.href='/product?category=<?php echo e($child->slug); ?>'">
                <img src="<?php echo e(asset('storage/' . $imagePath)); ?>" alt="">
                <span><?php echo e($child->name); ?></span>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- PHẦN DƯỚI: BỘ LỌC + SẢN PHẨM -->
    <div class="our-product-main">
        <!-- BỘ LỌC (STICKY) -->
        <aside class="our-product-filter">
            <?php echo $__env->make('medical::common.product-filter', [
            'filterAction' => route('our_product'),
            'sortOptions' => $filterData['sortOptions'] ?? [],
            'priceRanges' => $filterData['priceRanges'] ?? [],
            'brands' => $brands,
            'currentSort' => $filterData['currentSort'] ?? 'name_asc',
            'currentPriceRange' => $filterData['currentPriceRange'] ?? null,
            'currentBrands' => $filterData['currentBrands'] ?? []
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </aside>

        <!-- SẢN PHẨM -->
        <section class="our-product-list-section">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-[10px]">
                <?php $__empty_1 = true; $__currentLoopData = $products_category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php if(request('category') === 'quickorder' || request('category') === 'cost'): ?>
                
                <?php echo $__env->make('medical::common.product_card_quantity', [
                'product' => $product
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php else: ?>
                
                <?php echo $__env->make('medical::common.product_card', [
                'product' => $product
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center text-orange-500 font-medium my-8">
                    <?php if($searchKeyword): ?>
                    Không tìm thấy sản phẩm nào cho từ khóa "<?php echo e($searchKeyword); ?>"
                    <?php else: ?>
                    Hiện chưa có sản phẩm nào
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <div class="ingredient-pagination">
                <?php echo e($products_category->links('vendor.pagination.custom')); ?>

            </div>
        </section>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when sort changes
        const sortSelect = document.querySelector('select[name="sort"]');
        if (sortSelect) {
            sortSelect.addEventListener('change', function(e) {
                document.getElementById('product-filter-form').submit();
            });
        }
    });

    // Quantity controls for product_card_quantity (using event delegation)
    document.addEventListener('click', function(e) {
        // Handle minus button
        if (e.target.classList.contains('qty-btn-minus')) {
            e.preventDefault();
            e.stopPropagation();

            const productId = e.target.getAttribute('data-product-id');
            const input = document.querySelector(`.qty-input[data-product-id="${productId}"]`);
            let val = parseInt(input.value) || 1;
            if (val > 1) {
                input.value = val - 1;
                input.dispatchEvent(new Event('change'));
            }
        }

        // Handle plus button
        if (e.target.classList.contains('qty-btn-plus')) {
            e.preventDefault();
            e.stopPropagation();

            const productId = e.target.getAttribute('data-product-id');
            const input = document.querySelector(`.qty-input[data-product-id="${productId}"]`);
            let val = parseInt(input.value) || 1;
            input.value = val + 1;
            input.dispatchEvent(new Event('change'));
        }

        // Handle product quantity button
        if (e.target.classList.contains('product-quantity-btn')) {
            e.preventDefault();
            e.stopPropagation();

            const productId = e.target.getAttribute('data-product-id');
            const hasVisiblePrice = e.target.getAttribute('data-has-visible-price') === 'true';
            const isLoggedIn = e.target.getAttribute('data-is-logged-in') === 'true';

            if (!isLoggedIn) {
                showToast('Vui lòng đăng nhập để thực hiện chức năng này', 'info');
                setTimeout(() => {
                    window.location.href = '/signin';
                }, 1500);
                return;
            }

            // Get quantity
            const qtyInput = document.querySelector(`.qty-input[data-product-id="${productId}"]`);
            const quantity = qtyInput ? parseInt(qtyInput.value) : 1;

            if (hasVisiblePrice) {
                // Add to cart
                addToCart(productId, quantity);
            } else {
                // Request quote
                addToQuote(productId, quantity);
            }
        }
    });

    // Helper functions (same as product-card.js)
    function showToast(message, type = 'success') {
        // Kiểm tra xem Toastify đã được định nghĩa chưa
        if (typeof Toastify === 'function') {
            Toastify({
                text: message,
                duration: 3000, // Tăng thời gian hiển thị cho warning
                gravity: "top",
                position: "right",
                backgroundColor: type === 'success' ? "#4CAF50" : type === 'error' ? "#F44336" : type === 'warning' ? "#FF9800" : "#2196F3",
                close: true
            }).showToast();
        } else {
            // Fallback nếu không có Toastify
            alert(message);
        }
    }

    // Add to cart function (same logic as quickorder page)
    function addToCart(productId, quantity) {
        if (!productId) {
            showToast("Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.", 'error');
            return;
        }

        // Gọi API thêm vào giỏ hàng (same as quickorder)
        fetch('/api/medical/cart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hiển thị thông báo thành công
                    showToast(data.message, 'success');

                    // Cập nhật số lượng sản phẩm trong giỏ hàng (nếu có)
                    if (data.cart_count) {
                        const cartCountElement = document.querySelector('.cart-count');
                        if (cartCountElement) {
                            cartCountElement.textContent = data.cart_count;
                        }
                    }
                } else {
                    // Hiển thị thông báo lỗi
                    showToast(data.message, 'error');

                    // Nếu cần chuyển hướng đến trang đăng nhập
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast("Có lỗi xảy ra khi thêm vào giỏ hàng. Vui lòng thử lại sau.", 'error');
            });
    }

    // Add to quote function (same logic as cost page product-card.js)
    function addToQuote(productId, quantity) {
        if (!productId) {
            showToast("Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.", 'error');
            return;
        }

        // Ngăn chặn multiple API calls (same as product-card.js)
        const unitType = 1; // Default unit type
        const requestKey = `quote_${productId}_${quantity}_${unitType}`;
        if (window.pendingRequests && window.pendingRequests[requestKey]) {
            return;
        }

        if (!window.pendingRequests) {
            window.pendingRequests = {};
        }
        window.pendingRequests[requestKey] = true;

        // Gọi API thêm vào báo giá (same as product-card.js)
        fetch('/api/medical/quote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    product_id: parseInt(productId),
                    quantity: parseInt(quantity),
                    unit_type: parseInt(unitType)
                })
            })
            .then(response => response.json())
            .then(data => {
                // Cleanup pending request
                delete window.pendingRequests[requestKey];

                if (data.success) {
                    // Cập nhật số lượng sản phẩm trong báo giá (same as product-card.js)
                    updateQuoteCount(data.quote_count);

                    // Hiển thị thông báo với loại phù hợp
                    const toastType = data.is_out_of_stock ? 'warning' : 'success';
                    showToast(data.message || 'Đã thêm sản phẩm vào báo giá', toastType);
                } else {
                    // Xử lý lỗi
                    if (data.redirect) {
                        // Chuyển hướng đến trang đăng nhập nếu cần
                        window.location.href = data.redirect;
                    } else {
                        // Hiển thị thông báo lỗi
                        showToast(data.message || 'Đã xảy ra lỗi khi thêm sản phẩm vào báo giá', 'error');
                    }
                }
            })
            .catch(error => {
                // Cleanup pending request
                delete window.pendingRequests[requestKey];
                console.error('Error:', error);
                showToast('Đã xảy ra lỗi khi thêm sản phẩm vào báo giá', 'error');
            });
    }

    // Update quote count function (same as product-card.js)
    function updateQuoteCount(count) {
        // Cập nhật số lượng sản phẩm trong icon báo giá
        const quoteCountElement = document.querySelector('#header-quote-count');
        if (quoteCountElement) {
            quoteCountElement.textContent = count;

            // Hiển thị số lượng nếu > 0
            if (count > 0) {
                quoteCountElement.style.display = 'flex';
                quoteCountElement.style.justifyContent = 'center';
                quoteCountElement.style.alignItems = 'center';
            } else {
                quoteCountElement.style.display = 'none';
            }
        }
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/our_product/our_product.blade.php ENDPATH**/ ?>