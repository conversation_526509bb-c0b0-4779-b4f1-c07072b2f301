<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/ingredient_detail.css']); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/js/ingredient-detail.js']); ?>
<?php
    use Illuminate\Support\Facades\Storage;
?>
<div class="our-product-page">

    <!-- PHẦN DANH CHI TIẾT HOẠT CHẤT -->
    <div class="ingredient-detail-card">
        <div class="ingredient-detail-info">
            <div class="ingredient-detail-title"><?php echo e($activeIngredient->name); ?></div>
            <div class="ingredient-detail-desc">
                <?php echo nl2br(e($activeIngredient->description)); ?>

            </div>
        </div>
    </div>

    <!-- PHẦN DƯỚI: BỘ LỌC + SẢN PHẨM -->
    <div class="our-product-main">
        <!-- BỘ LỌC (STICKY) -->
        <aside class="our-product-filter">
            <?php echo $__env->make('medical::common.product-filter', [
                'filterAction' => route('ingredient_detail', $activeIngredient->slug),
                'sortOptions' => $filterData['sortOptions'] ?? [],
                'priceRanges' => $filterData['priceRanges'] ?? [],
                'brands' => $brands,
                'currentSort' => $filterData['currentSort'] ?? 'name_asc',
                'currentPriceRange' => $filterData['currentPriceRange'] ?? null,
                'currentBrands' => $filterData['currentBrands'] ?? []
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </aside>


        <!-- SẢN PHẨM -->
        <?php if($products->count() > 0): ?>
        <section class="our-product-list-section">
            <div class="our-product-list">
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('medical::common.product_card', [
                                'product' => $product
                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php echo e($products->links('vendor.pagination.custom')); ?>

        </section>
        <?php else: ?>
        <section class="our-product-list-section">
            <p class="our-product-list-empty">Không có sản phẩm nào chứa hoạt chất này</p>
        </section>
        <?php endif; ?>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // Thiết lập lại bộ lọc và chuyển hướng đến trang không có tham số
        document.querySelector('.filter-reset').addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = this.getAttribute('href');
        });

        // Tự động submit form khi chọn radio button
        document.querySelectorAll('.filter-price-options input[type="radio"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                document.getElementById('filter-form').submit();
            });
        });

        // Tự động submit form khi chọn checkbox thương hiệu
        document.querySelectorAll('.filter-brand-list input[type="checkbox"]').forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                document.getElementById('filter-form').submit();
            });
        });

        // 3. Lọc thương hiệu theo input
        var brandSearch = document.querySelector('.filter-brand-search');
        if (brandSearch) {
            brandSearch.addEventListener('input', function() {
                var keyword = brandSearch.value.trim().toLowerCase();
                document.querySelectorAll('.filter-brand-list label').forEach(function(label) {
                    var text = label.textContent.trim().toLowerCase();
                    if (text.includes(keyword)) {
                        label.style.display = '';
                    } else {
                        label.style.display = 'none';
                    }
                });
            });
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
    const pagination = document.querySelector('.our-product-pagination');
    if (!pagination) return;

    // Số trang, bạn có thể thay đổi nếu cần
    const totalPages = 3;

    pagination.addEventListener('click', function(e) {
        if (!e.target.classList.contains('pagination-btn')) return;

        let page = e.target.getAttribute('data-page');
        let newActiveBtn;

        if (page === 'first') {
            // Về trang đầu
            newActiveBtn = pagination.querySelector('.pagination-btn[data-page="1"]');
        } else if (page === 'last') {
            // Về trang cuối
            newActiveBtn = pagination.querySelector('.pagination-btn[data-page="' + totalPages + '"]');
        } else {
            // Trang số
            newActiveBtn = e.target;
        }

        // Đổi active
        pagination.querySelectorAll('.pagination-btn').forEach(btn => btn.classList.remove('active'));
        if (newActiveBtn) newActiveBtn.classList.add('active');

        // TODO: Thay đổi dữ liệu sản phẩm theo trang ở đây nếu cần
        // console.log('Chuyển đến trang:', newActiveBtn.textContent.trim());
    });
});
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/ingredient_detail/ingredient_detail.blade.php ENDPATH**/ ?>