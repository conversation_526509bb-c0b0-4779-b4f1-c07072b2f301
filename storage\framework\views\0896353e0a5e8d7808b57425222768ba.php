<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html
    lang="<?php echo e(app()->getLocale()); ?>"
    dir="<?php echo e(core()->getCurrentLocale()->direction); ?>"
>
    <head>
        <!-- meta tags -->
        <meta http-equiv="Cache-control" content="no-cache">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

        <?php
            $fontPath = [];

            $fontFamily = [
                'regular' => 'Arial, sans-serif',
                'bold'    => 'Arial, sans-serif',
            ];

            if (in_array(app()->getLocale(), ['ar', 'he', 'fa', 'tr', 'ru', 'uk'])) {
                $fontFamily = [
                    'regular' => 'DejaVu Sans',
                    'bold'    => 'DejaVu Sans',
                ];
            } elseif (app()->getLocale() == 'zh_CN') {
                $fontPath = [
                    'regular' => asset('fonts/NotoSansSC-Regular.ttf'),
                    'bold'    => asset('fonts/NotoSansSC-Bold.ttf'),
                ];

                $fontFamily = [
                    'regular' => 'Noto Sans SC',
                    'bold'    => 'Noto Sans SC Bold',
                ];
            } elseif (app()->getLocale() == 'ja') {
                $fontPath = [
                    'regular' => asset('fonts/NotoSansJP-Regular.ttf'),
                    'bold'    => asset('fonts/NotoSansJP-Bold.ttf'),
                ];

                $fontFamily = [
                    'regular' => 'Noto Sans JP',
                    'bold'    => 'Noto Sans JP Bold',
                ];
            } elseif (app()->getLocale() == 'hi_IN') {
                $fontPath = [
                    'regular' => asset('fonts/Hind-Regular.ttf'),
                    'bold'    => asset('fonts/Hind-Bold.ttf'),
                ];

                $fontFamily = [
                    'regular' => 'Hind',
                    'bold'    => 'Hind Bold',
                ];
            } elseif (app()->getLocale() == 'bn') {
                $fontPath = [
                    'regular' => asset('fonts/NotoSansBengali-Regular.ttf'),
                    'bold'    => asset('fonts/NotoSansBengali-Bold.ttf'),
                ];

                $fontFamily = [
                    'regular' => 'Noto Sans Bengali',
                    'bold'    => 'Noto Sans Bengali Bold',
                ];
            } elseif (app()->getLocale() == 'sin') {
                $fontPath = [
                    'regular' => asset('fonts/NotoSansSinhala-Regular.ttf'),
                    'bold'    => asset('fonts/NotoSansSinhala-Bold.ttf'),
                ];

                $fontFamily = [
                    'regular' => 'Noto Sans Sinhala',
                    'bold'    => 'Noto Sans Sinhala Bold',
                ];
            }
        ?>

        <!-- lang supports inclusion -->
        <style type="text/css">
            <?php if(! empty($fontPath['regular'])): ?>
                @font-face {
                    src: url(<?php echo e($fontPath['regular']); ?>) format('truetype');
                    font-family: <?php echo e($fontFamily['regular']); ?>;
                }
            <?php endif; ?>

            <?php if(! empty($fontPath['bold'])): ?>
                @font-face {
                    src: url(<?php echo e($fontPath['bold']); ?>) format('truetype');
                    font-family: <?php echo e($fontFamily['bold']); ?>;
                    font-style: bold;
                }
            <?php endif; ?>

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: <?php echo e($fontFamily['regular']); ?>;
            }

            body {
                font-size: 10px;
                color: #091341;
                font-family: "<?php echo e($fontFamily['regular']); ?>";
            }

            b, th {
                font-family: "<?php echo e($fontFamily['bold']); ?>";
            }

            .page-content {
                padding: 12px;
            }

            .page-header {
                border-bottom: 1px solid #E9EFFC;
                text-align: center;
                font-size: 24px;
                text-transform: uppercase;
                color: #000DBB;
                padding: 24px 0;
                margin: 0;
            }

            .logo-container {
                position: absolute;
                top: 20px;
                left: 20px;
            }

            .logo-container.rtl {
                left: auto;
                right: 20px;
            }

            .logo-container img {
                max-width: 100%;
                height: auto;
            }

            .page-header b {
                display: inline-block;
                vertical-align: middle;
            }

            .small-text {
                font-size: 7px;
            }

            table {
                width: 100%;
                border-spacing: 1px 0;
                border-collapse: separate;
                margin-bottom: 16px;
            }

            table thead th {
                background-color: #E9EFFC;
                color: #000DBB;
                padding: 6px 18px;
                text-align: left;
            }

            table.rtl thead tr th {
                text-align: right;
            }

            table tbody td {
                padding: 9px 18px;
                border-bottom: 1px solid #E9EFFC;
                text-align: left;
                vertical-align: top;
            }

            table.rtl tbody tr td {
                text-align: right;
            }

            .summary {
                width: 100%;
                display: inline-block;
            }

            .summary table {
                float: right;
                width: 250px;
                padding-top: 5px;
                padding-bottom: 5px;
                background-color: #E9EFFC;
                white-space: nowrap;
            }

            .summary table.rtl {
                width: 280px;
            }

            .summary table.rtl {
                margin-right: 480px;
            }

            .summary table td {
                padding: 5px 10px;
            }

            .summary table td:nth-child(2) {
                text-align: center;
            }

            .summary table td:nth-child(3) {
                text-align: right;
            }
        </style>
    </head>

    <body dir="<?php echo e(core()->getCurrentLocale()->direction); ?>">
        <div class="logo-container <?php echo e(core()->getCurrentLocale()->direction); ?>">
            <?php if(core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo')): ?>
                <img src="data:image/png;base64,<?php echo e(base64_encode(file_get_contents(Storage::url(core()->getConfigData('sales.invoice_settings.pdf_print_outs.logo'))))); ?>"/>
            <?php else: ?>
                <img src="data:image/png;base64,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"/>
            <?php endif; ?>
        </div>

        <div class="page">
            <!-- Header -->
            <div class="page-header">
                <b><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.invoice'); ?></b>
            </div>

            <div class="page-content">
                <!-- Invoice Information -->
                <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                    <tbody>
                        <tr>
                            <?php if(core()->getConfigData('sales.invoice_settings.pdf_print_outs.invoice_id')): ?>
                                <td style="width: 50%; padding: 2px 18px;border:none;">
                                    <b>
                                        <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.invoice-id'); ?>:
                                    </b>

                                    <span>
                                        #<?php echo e($invoice->increment_id ?? $invoice->id); ?>

                                    </span>
                                </td>
                            <?php endif; ?>

                            <?php if(core()->getConfigData('sales.invoice_settings.pdf_print_outs.order_id')): ?>
                                <td style="width: 50%; padding: 2px 18px;border:none;">
                                    <b>
                                        <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.order-id'); ?>:
                                    </b>

                                    <span>
                                        #<?php echo e($invoice->order->increment_id); ?>

                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>

                        <tr>
                            <td style="width: 50%; padding: 2px 18px;border:none;">
                                <b>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.date'); ?>:
                                </b>

                                <span>
                                    <?php echo e(core()->formatDate($invoice->created_at, 'd-m-Y')); ?>

                                </span>
                            </td>

                            <td style="width: 50%; padding: 2px 18px;border:none;">
                                <b>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.order-date'); ?>:
                                </b>

                                <span>
                                    <?php echo e(core()->formatDate($invoice->order->created_at, 'd-m-Y')); ?>

                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- Invoice Information -->
                <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                    <tbody>
                        <tr>
                            <?php if(! empty(core()->getConfigData('sales.shipping.origin.country'))): ?>
                                <td style="width: 50%; padding: 2px 18px;border:none;">
                                    <b style="display: inline-block; margin-bottom: 4px;">
                                        <?php echo e(core()->getConfigData('sales.shipping.origin.store_name')); ?>

                                    </b>

                                    <div>
                                        <div><?php echo e(core()->getConfigData('sales.shipping.origin.address')); ?></div>

                                        <div><?php echo e(core()->getConfigData('sales.shipping.origin.zipcode') . ' ' . core()->getConfigData('sales.shipping.origin.city')); ?></div>

                                        <div><?php echo e(core()->getConfigData('sales.shipping.origin.state') . ', ' . core()->getConfigData('sales.shipping.origin.country')); ?></div>
                                    </div>
                                </td>
                            <?php endif; ?>

                            <td style="width: 50%; padding: 2px 18px;border:none;">
                                <?php if($invoice->hasPaymentTerm()): ?>
                                    <div style="margin-bottom: 12px">
                                        <b style="display: inline-block; margin-bottom: 4px;">
                                            <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.payment-terms'); ?>:
                                        </b>

                                        <span>
                                            <?php echo e($invoice->getFormattedPaymentTerm()); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if(core()->getConfigData('sales.shipping.origin.bank_details')): ?>
                                    <div>
                                        <b style="display: inline-block; margin-bottom: 4px;">
                                            <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.bank-details'); ?>:
                                        </b>

                                        <div>
                                            <?php echo nl2br(core()->getConfigData('sales.shipping.origin.bank_details')); ?>

                                        </div>
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- Billing & Shipping Address -->
                <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                    <thead>
                        <tr>
                            <?php if($invoice->order->billing_address): ?>
                                <th style="width: 50%;">
                                    <b>
                                        <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.bill-to'); ?>
                                    </b>
                                </th>
                            <?php endif; ?>

                            <?php if($invoice->order->shipping_address): ?>
                                <th style="width: 50%">
                                    <b>
                                        <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.ship-to'); ?>
                                    </b>
                                </th>
                            <?php endif; ?>
                        </tr>
                    </thead>

                    <tbody>
                        <tr>
                            <?php if($invoice->order->billing_address): ?>
                                <td style="width: 50%">
                                    <div><?php echo e($invoice->order->billing_address->company_name ?? ''); ?><div>

                                    <div><?php echo e($invoice->order->billing_address->name); ?></div>

                                    <div><?php echo e($invoice->order->billing_address->address); ?></div>

                                    <div><?php echo e($invoice->order->billing_address->postcode . ' ' . $invoice->order->billing_address->city); ?></div>

                                    <div><?php echo e($invoice->order->billing_address->state . ', ' . core()->country_name($invoice->order->billing_address->country)); ?></div>

                                    <div><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.contact'); ?>: <?php echo e($invoice->order->billing_address->phone); ?></div>
                                </td>
                            <?php endif; ?>

                            <?php if($invoice->order->shipping_address): ?>
                                <td style="width: 50%">
                                    <div><?php echo e($invoice->order->shipping_address->company_name ?? ''); ?><div>

                                    <div><?php echo e($invoice->order->shipping_address->name); ?></div>

                                    <div><?php echo e($invoice->order->shipping_address->address); ?></div>

                                    <div><?php echo e($invoice->order->shipping_address->postcode . ' ' . $invoice->order->shipping_address->city); ?></div>

                                    <div><?php echo e($invoice->order->shipping_address->state . ', ' . core()->country_name($invoice->order->shipping_address->country)); ?></div>

                                    <div><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.contact'); ?>: <?php echo e($invoice->order->shipping_address->phone); ?></div>
                                </td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>

                <!-- Payment & Shipping Methods -->
                <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                    <thead>
                        <tr>
                            <th style="width: 50%">
                                <b>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.payment-method'); ?>
                                </b>
                            </th>

                            <?php if($invoice->order->shipping_address): ?>
                                <th style="width: 50%">
                                    <b>
                                        <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.shipping-method'); ?>
                                    </b>
                                </th>
                            <?php endif; ?>
                        </tr>
                    </thead>

                    <tbody>
                        <tr>
                            <td style="width: 50%">
                                <?php echo e(core()->getConfigData('sales.payment_methods.' . $invoice->order->payment->method . '.title')); ?>


                                <?php $additionalDetails = \Webkul\Payment\Payment::getAdditionalDetails($invoice->order->payment->method); ?>

                                <?php if(! empty($additionalDetails)): ?>
                                    <div class="row small-text">
                                        <span><?php echo e($additionalDetails['title']); ?>:</span>

                                        <span><?php echo e($additionalDetails['value']); ?></span>
                                    </div>
                                <?php endif; ?>
                            </td>

                            <?php if($invoice->order->shipping_address): ?>
                                <td style="width: 50%">
                                    <?php echo e($invoice->order->shipping_title); ?>

                                </td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>

                <!-- Items -->
                <div class="items">
                    <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                        <thead>
                            <tr>
                                <th>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.sku'); ?>
                                </th>

                                <th>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.product-name'); ?>
                                </th>

                                <th>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.price'); ?>
                                </th>

                                <th>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.qty'); ?>
                                </th>

                                <th>
                                    <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.subtotal'); ?>
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php $__currentLoopData = $invoice->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php echo e($item->getTypeInstance()->getOrderedItem($item)->sku); ?>

                                    </td>

                                    <td>
                                        <?php echo e($item->name); ?>


                                        <?php if(isset($item->additional['attributes'])): ?>
                                            <div>
                                                <?php $__currentLoopData = $item->additional['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(
                                                        ! isset($attribute['attribute_type'])
                                                        || $attribute['attribute_type'] !== 'file'
                                                    ): ?>
                                                        <b><?php echo e($attribute['attribute_name']); ?> : </b><?php echo e($attribute['option_label']); ?><br>
                                                    <?php else: ?>
                                                        <?php echo e($attribute['attribute_name']); ?> :

                                                        <a
                                                            href="<?php echo e(Storage::url($attribute['option_label'])); ?>"
                                                            class="text-blue-600 hover:underline"
                                                            download="<?php echo e(File::basename($attribute['option_label'])); ?>"
                                                        >
                                                            <?php echo e(File::basename($attribute['option_label'])); ?>

                                                        </a>

                                                        <br>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>

                                    <td>
                                        <?php if(core()->getConfigData('sales.taxes.sales.display_prices') == 'including_tax'): ?>
                                            <?php echo core()->formatBasePrice($item->base_price_incl_tax, true); ?>

                                        <?php elseif(core()->getConfigData('sales.taxes.sales.display_prices') == 'both'): ?>
                                            <?php echo core()->formatBasePrice($item->base_price_incl_tax, true); ?>


                                            <div class="small-text">
                                                <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.excl-tax'); ?>

                                                <span>
                                                    <?php echo e(core()->formatPrice($item->base_price)); ?>

                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <?php echo core()->formatBasePrice($item->base_price, true); ?>

                                        <?php endif; ?>
                                    </td>

                                    <td>
                                        <?php echo e($item->qty); ?>

                                    </td>

                                    <td>
                                        <?php if(core()->getConfigData('sales.taxes.sales.display_subtotal') == 'including_tax'): ?>
                                            <?php echo core()->formatBasePrice($item->base_total_incl_tax, true); ?>

                                        <?php elseif(core()->getConfigData('sales.taxes.sales.display_subtotal') == 'both'): ?>
                                            <?php echo core()->formatBasePrice($item->base_total_incl_tax, true); ?>


                                            <div class="small-text">
                                                <?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.excl-tax'); ?>

                                                <span>
                                                    <?php echo e(core()->formatPrice($item->base_total)); ?>

                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <?php echo core()->formatBasePrice($item->base_total, true); ?>

                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Table -->
                <div class="summary">
                    <table class="<?php echo e(core()->getCurrentLocale()->direction); ?>">
                        <tbody>
                            <?php if(core()->getConfigData('sales.taxes.sales.display_subtotal') == 'including_tax'): ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.subtotal'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_sub_total_incl_tax, true); ?></td>
                                </tr>
                            <?php elseif(core()->getConfigData('sales.taxes.sales.display_subtotal') == 'both'): ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.subtotal-incl-tax'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_sub_total_incl_tax, true); ?></td>
                                </tr>

                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.subtotal-excl-tax'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_sub_total, true); ?></td>
                                </tr>
                            <?php else: ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.subtotal'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_sub_total, true); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if(core()->getConfigData('sales.taxes.sales.display_shipping_amount') == 'including_tax'): ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.shipping-handling'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_shipping_amount_incl_tax, true); ?></td>
                                </tr>
                            <?php elseif(core()->getConfigData('sales.taxes.sales.display_shipping_amount') == 'both'): ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.shipping-handling-incl-tax'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_shipping_amount_incl_tax, true); ?></td>
                                </tr>

                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.shipping-handling-excl-tax'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_shipping_amount, true); ?></td>
                                </tr>
                            <?php else: ?>
                                <tr>
                                    <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.shipping-handling'); ?></td>
                                    <td>-</td>
                                    <td><?php echo core()->formatBasePrice($invoice->base_shipping_amount, true); ?></td>
                                </tr>
                            <?php endif; ?>

                            <tr>
                                <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.tax'); ?></td>
                                <td>-</td>
                                <td><?php echo core()->formatBasePrice($invoice->base_tax_amount, true); ?></td>
                            </tr>

                            <tr>
                                <td><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.discount'); ?></td>
                                <td>-</td>
                                <td><?php echo core()->formatBasePrice($invoice->base_discount_amount, true); ?></td>
                            </tr>

                            <tr>
                                <td style="border-top: 1px solid #FFFFFF;">
                                    <b><?php echo app('translator')->get('shop::app.customers.account.orders.invoice-pdf.grand-total'); ?></b>
                                </td>
                                <td style="border-top: 1px solid #FFFFFF;">-</td>
                                <td style="border-top: 1px solid #FFFFFF;">
                                    <b><?php echo core()->formatBasePrice($invoice->base_grand_total, true); ?></b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </body>
</html>
<?php /**PATH /var/www/html/packages/Webkul/Shop/src/Resources/views/customers/account/orders/pdf.blade.php ENDPATH**/ ?>