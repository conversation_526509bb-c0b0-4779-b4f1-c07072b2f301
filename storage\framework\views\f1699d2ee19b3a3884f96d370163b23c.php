<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Thêm font Be Vietnam Pro giống với app layout -->
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap" rel="stylesheet">
    <?php echo app('App\Services\MedicalViteService')->renderAssets('resources/themes/medical/css/signup.css'); ?>
    <!-- Thêm Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Thêm Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <title>Đ<PERSON>ng ký - Medical Shop</title>
    
    <style>
        body, input, button, select {
            font-family: 'Be Vietnam Pro', sans-serif !important;
        }
    </style>
</head>

<body>
    <div class="signup-container">
        <a href="/home" class="logo">
            <img src="/images/full_logo_footer.png" alt="Velocity Logo">
        </a>

        <div class="signup-form">
            <h1>Đăng ký tài khoản</h1>
            <p class="subtitle">Nếu bạn chưa có tài khoản, chúng tôi rất vui được chào đón bạn.</p>

            <?php if(session('success')): ?>
                <div class="alert alert-success" style="color: green; background-color: #d4edda; padding: 10px; border-radius: 4px; margin-bottom: 20px;">
                    <p><?php echo e(session('success')); ?></p>
                </div>
            <?php endif; ?>

            <form id="form-signup" action="<?php echo e(route('signup.submit')); ?>" method="POST" novalidate>
                <?php echo csrf_field(); ?>
                <div class="form-row">
                    <div class="form-group">
                        <label for="fullname">Họ và tên <span class="required">*</span></label>
                        <input type="text" id="fullname" name="fullname" placeholder="Nhập họ và tên"
                            value="<?php echo e(old('fullname')); ?>"
                            class="<?php echo e(session('error_fullname') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_fullname')): ?>
                            <span class="error-message"><?php echo e(session('error_fullname')); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="phone">Số điện thoại <span class="required">*</span></label>
                        <input type="tel" id="phone" name="phone" placeholder="Nhập số điện thoại"
                            value="<?php echo e(old('phone')); ?>"
                            class="<?php echo e(session('error_phone') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_phone')): ?>
                            <span class="error-message"><?php echo e(session('error_phone')); ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="date_of_birth">Ngày sinh <span class="required">*</span></label>
                        <input type="text" id="date_of_birth" name="date_of_birth" placeholder="dd/mm/yyyy"
                            value="<?php echo e(old('date_of_birth')); ?>"
                            class="<?php echo e(session('error_date_of_birth') ? 'is-invalid' : ''); ?> flatpickr-input"
                            autocomplete="off"
                            required>
                        <?php if(session('error_date_of_birth')): ?>
                            <span class="error-message"><?php echo e(session('error_date_of_birth')); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label>Giới tính <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="gender-male" name="gender" value="male" <?php echo e(old('gender') == 'male' ? 'checked' : ''); ?> required>
                                <label for="gender-male">Nam</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="gender-female" name="gender" value="female" <?php echo e(old('gender') == 'female' ? 'checked' : ''); ?>>
                                <label for="gender-female">Nữ</label>
                            </div>
                        </div>
                        <?php if(session('error_gender')): ?>
                            <span class="error-message"><?php echo e(session('error_gender')); ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" placeholder="Nhập email"
                        value="<?php echo e(old('email')); ?>"
                        class="<?php echo e(session('error_email') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if(session('error_email')): ?>
                        <span class="error-message"><?php echo e(session('error_email')); ?></span>
                    <?php endif; ?>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Mật khẩu <span class="required">*</span></label>
                        <input type="password" id="password" name="password" placeholder="Nhập mật khẩu"
                            class="<?php echo e(session('error_password') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_password')): ?>
                            <span class="error-message"><?php echo e(session('error_password')); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="form-group">
                        <label for="password_confirmation">Xác nhận mật khẩu <span class="required">*</span></label>
                        <input type="password" id="password_confirmation" name="password_confirmation" placeholder="Nhập lại mật khẩu" required>
                    </div>
                </div>

                <div class="business-info">
                    <h2>Thông tin doanh nghiệp</h2>

                    <div class="form-group">
                        <label for="company-name">Tên doanh nghiệp <span class="required">*</span></label>
                        <input type="text" id="company-name" name="company_name" placeholder="Nhập tên doanh nghiệp"
                            value="<?php echo e(old('company_name')); ?>"
                            class="<?php echo e(session('error_company_name') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_company_name')): ?>
                            <span class="error-message"><?php echo e(session('error_company_name')); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="business-type">Loại hình doanh nghiệp <span class="required">*</span></label>
                        <select id="business-type" name="company_type"
                            class="<?php echo e(session('error_company_type') ? 'is-invalid' : ''); ?>"
                            required>
                            <option value="">-- Chọn loại hình doanh nghiệp --</option>
                            <option value="llc" <?php echo e(old('company_type') == 'llc' ? 'selected' : ''); ?>>Công ty TNHH</option>
                            <option value="jsc" <?php echo e(old('company_type') == 'jsc' ? 'selected' : ''); ?>>Công ty cổ phần</option>
                            <option value="private" <?php echo e(old('company_type') == 'private' ? 'selected' : ''); ?>>Doanh nghiệp tư nhân</option>
                        </select>
                        <?php if(session('error_company_type')): ?>
                            <span class="error-message"><?php echo e(session('error_company_type')); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="tax-code">Mã số thuế <span class="required">*</span></label>
                        <input type="text" id="tax-code" name="tax_code" placeholder="Nhập mã số thuế"
                            value="<?php echo e(old('tax_code')); ?>"
                            class="<?php echo e(session('error_tax_code') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_tax_code')): ?>
                            <span class="error-message"><?php echo e(session('error_tax_code')); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <label for="address">Địa chỉ <span class="required">*</span></label>
                        <input type="text" id="address" name="company_address" placeholder="Nhập địa chỉ"
                            value="<?php echo e(old('company_address')); ?>"
                            class="<?php echo e(session('error_company_address') ? 'is-invalid' : ''); ?>"
                            required>
                        <?php if(session('error_company_address')): ?>
                            <span class="error-message"><?php echo e(session('error_company_address')); ?></span>
                        <?php endif; ?>
                    </div>

                    
                </div>

                <div class="form-options">
                    <div class="checkbox-group" style="display: flex; flex-direction: column;">
                        <div style="display: flex; align-items: flex-start;">
                            <input type="checkbox" id="terms" name="terms" required class="<?php echo e(session('error_terms') ? 'is-invalid' : ''); ?>" style="margin-top: 5px;">
                            <label for="terms" class="label-show-password">Tôi đồng ý với <a href="/page/dieu-khoan" class="terms-link">điều khoản và điều kiện</a></label>
                        </div>
                        <?php if(session('error_terms')): ?>
                            <span class="error-message" style="margin-left: 24px; margin-top: 4px;"><?php echo e(session('error_terms')); ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <button type="submit" class="register-btn">Đăng ký</button>

                <div class="login-account">
                    <span>Bạn đã có tài khoản?</span>
                    <a href="/signin">Đăng nhập</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Xử lý hiển thị/ẩn thông tin doanh nghiệp
            const businessRadio = document.getElementById('business');
            const personalRadio = document.getElementById('personal');
            const businessInfo = document.querySelector('.business-info');

            function toggleBusinessInfo() {
                businessInfo.style.display = businessRadio && personalRadio ?
                    (businessRadio.checked ? 'block' : 'none') : 'block';
            }

            if (businessRadio && personalRadio) {
                businessRadio.addEventListener('change', toggleBusinessInfo);
                personalRadio.addEventListener('change', toggleBusinessInfo);
            }

            // Xử lý upload file
            /* const fileUploadBtn = document.querySelector('.file-upload-btn');
            const fileInput = document.getElementById('business-license');
            const fileName = document.querySelector('.file-name');

            if (fileUploadBtn && fileInput && fileName) {
                fileUploadBtn.addEventListener('click', function() {
                    fileInput.click();
                });

                fileInput.addEventListener('change', function() {
                    if (fileInput.files.length > 0) {
                        fileName.textContent = fileInput.files[0].name;
                    } else {
                        fileName.textContent = 'Không có tệp nào được chọn';
                    }
                });
            } */

            // Khởi tạo hiển thị
            toggleBusinessInfo();

            // Xử lý validation form
            const form = document.getElementById('form-signup');

            // Ngăn chặn validation mặc định của trình duyệt
            if (form) {
                form.setAttribute('novalidate', true);

                // Xử lý sự kiện submit form
                form.addEventListener('submit', function(event) {
                    let isValid = true;

                    // Kiểm tra các trường bắt buộc
                    const requiredFields = form.querySelectorAll('[required]');
                    requiredFields.forEach(function(field) {
                        // Xóa thông báo lỗi cũ nếu có
                        if (field.type === 'checkbox' && field.id === 'terms') {
                            // Cho checkbox terms, xóa tất cả error message trong container cha (trừ server error)
                            const existingErrors = field.parentElement.parentElement.querySelectorAll('.error-message:not([data-server-error])');
                            existingErrors.forEach(error => error.remove());
                        } else {
                            const existingError = field.parentElement.querySelector('.error-message:not([data-server-error])');
                            if (existingError) {
                                existingError.remove();
                            }
                        }

                        // Kiểm tra checkbox điều khoản
                        if (field.type === 'checkbox' && field.id === 'terms') {
                            if (!field.checked) {
                                isValid = false;
                                field.classList.add('is-invalid');

                                // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                                if (!serverError) {
                                    // Tạo thông báo lỗi
                                    const errorMessage = document.createElement('span');
                                    errorMessage.className = 'error-message';
                                    errorMessage.textContent = 'Vui lòng đồng ý với điều khoản và điều kiện.';
                                    errorMessage.style.marginTop = '4px';
                                    errorMessage.style.display = 'block';

                                    // Thêm thông báo lỗi vào sau container
                                    field.parentElement.parentElement.appendChild(errorMessage);
                                }
                            } else {
                                // Nếu checkbox đã được check, xóa class is-invalid
                                field.classList.remove('is-invalid');
                            }
                        }
                        // Kiểm tra trường có giá trị hay không (cho các field khác)
                        else if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('is-invalid');

                            // Kiểm tra xem đã có thông báo lỗi từ server chưa
                            const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                            if (!serverError) {
                                // Tạo thông báo lỗi
                                const errorMessage = document.createElement('span');
                                errorMessage.className = 'error-message';
                                errorMessage.textContent = 'Vui lòng nhập thông tin này.';

                                // Thêm thông báo lỗi vào sau field
                                field.parentElement.appendChild(errorMessage);
                            }
                        } else {
                            // Nếu trường đã có giá trị, xóa class is-invalid
                            field.classList.remove('is-invalid');
                        }

                        // Kiểm tra định dạng email
                        if (field.type === 'email' && field.value.trim()) {
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test(field.value.trim())) {
                                isValid = false;
                                field.classList.add('is-invalid');

                                // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                                if (!serverError) {
                                    // Tạo thông báo lỗi
                                    const errorMessage = document.createElement('span');
                                    errorMessage.className = 'error-message';
                                    errorMessage.textContent = 'Email không đúng định dạng.';

                                    // Thêm thông báo lỗi vào sau field
                                    field.parentElement.appendChild(errorMessage);
                                }
                            }
                        }

                        // Kiểm tra định dạng số điện thoại
                        if (field.id === 'phone' && field.value.trim()) {
                            const phoneRegex = /^[0-9]{10,11}$/;
                            if (!phoneRegex.test(field.value.trim())) {
                                isValid = false;
                                field.classList.add('is-invalid');

                                // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                                if (!serverError) {
                                    // Tạo thông báo lỗi
                                    const errorMessage = document.createElement('span');
                                    errorMessage.className = 'error-message';
                                    errorMessage.textContent = 'Số điện thoại không hợp lệ.';

                                    // Thêm thông báo lỗi vào sau field
                                    field.parentElement.appendChild(errorMessage);
                                }
                            }
                        }

                        // Kiểm tra độ dài mật khẩu
                        if (field.id === 'password' && field.value.trim()) {
                            if (field.value.trim().length < 6) {
                                isValid = false;
                                field.classList.add('is-invalid');

                                // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                                if (!serverError) {
                                    // Tạo thông báo lỗi
                                    const errorMessage = document.createElement('span');
                                    errorMessage.className = 'error-message';
                                    errorMessage.textContent = 'Mật khẩu phải có ít nhất 6 ký tự.';

                                    // Thêm thông báo lỗi vào sau field
                                    field.parentElement.appendChild(errorMessage);
                                }
                            } else {
                                // Kiểm tra mật khẩu xác nhận nếu password hợp lệ
                                const confirmPassword = document.getElementById('password_confirmation');
                                if (confirmPassword && field.value !== confirmPassword.value) {
                                    isValid = false;
                                    confirmPassword.classList.add('is-invalid');

                                    // Xóa thông báo lỗi cũ nếu có
                                    const existingError = confirmPassword.parentElement.querySelector('.error-message:not([data-server-error])');
                                    if (existingError) {
                                        existingError.remove();
                                    }

                                    // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                    const serverError = confirmPassword.parentElement.querySelector('.error-message[data-server-error]');
                                    if (!serverError) {
                                        // Tạo thông báo lỗi
                                        const errorMessage = document.createElement('span');
                                        errorMessage.className = 'error-message';
                                        errorMessage.textContent = 'Mật khẩu xác nhận không khớp.';

                                        // Thêm thông báo lỗi vào sau field
                                        confirmPassword.parentElement.appendChild(errorMessage);
                                    }
                                }
                            }
                        }
                    });

                    // Kiểm tra mật khẩu xác nhận
                    const passwordField = document.getElementById('password');
                    const confirmPasswordField = document.getElementById('password_confirmation');
                    if (passwordField && confirmPasswordField && confirmPasswordField.value) {
                        if (passwordField.value !== confirmPasswordField.value) {
                            isValid = false;
                            confirmPasswordField.classList.add('is-invalid');

                            // Kiểm tra xem đã có thông báo lỗi từ server chưa
                            const serverError = confirmPasswordField.parentElement.querySelector('.error-message[data-server-error]');
                            if (!serverError) {
                                // Tạo thông báo lỗi giống như các trường khác
                                const errorMessage = document.createElement('span');
                                errorMessage.className = 'error-message';
                                errorMessage.textContent = 'Mật khẩu xác nhận không khớp.';

                                // Thêm thông báo lỗi vào sau field
                                confirmPasswordField.parentElement.appendChild(errorMessage);
                            }
                        }
                    }

                    // Nếu form không hợp lệ, ngăn chặn submit
                    if (!isValid) {
                        event.preventDefault();
                    }
                });

                // Đánh dấu các thông báo lỗi từ server
                const serverErrors = document.querySelectorAll('.error-message');
                serverErrors.forEach(function(error) {
                    error.setAttribute('data-server-error', 'true');
                });

                // Thêm sự kiện lắng nghe input để ẩn lỗi khi người dùng nhập
                const inputFields = form.querySelectorAll('input, select, textarea');
                inputFields.forEach(function(field) {
                    // Xử lý sự kiện input (khi người dùng nhập)
                    field.addEventListener('input', function() {
                        // Xóa thông báo lỗi
                        if (field.type === 'checkbox' && field.id === 'terms') {
                            // Cho checkbox terms, xóa tất cả error message trong container cha
                            const errorMessages = field.parentElement.parentElement.querySelectorAll('.error-message');
                            errorMessages.forEach(error => error.remove());
                        } else {
                            const errorMessage = field.parentElement.querySelector('.error-message');
                            if (errorMessage) {
                                errorMessage.remove();
                            }
                        }

                        // Xóa class is-invalid
                        field.classList.remove('is-invalid');
                    });

                    // Xử lý sự kiện change (cho select)
                    field.addEventListener('change', function() {
                        // Xóa thông báo lỗi
                        if (field.type === 'checkbox' && field.id === 'terms') {
                            // Cho checkbox terms, xóa tất cả error message trong container cha
                            const errorMessages = field.parentElement.parentElement.querySelectorAll('.error-message');
                            errorMessages.forEach(error => error.remove());
                        } else {
                            const errorMessage = field.parentElement.querySelector('.error-message');
                            if (errorMessage) {
                                errorMessage.remove();
                            }
                        }

                        // Xóa class is-invalid
                        field.classList.remove('is-invalid');
                    });
                });

                // Xử lý đặc biệt cho password_confirmation
                const passwordField = document.getElementById('password');
                const confirmPasswordField = document.getElementById('password_confirmation');

                if (passwordField && confirmPasswordField) {
                    function checkPasswordMatch() {
                        // Xóa thông báo lỗi cũ
                        const existingError = confirmPasswordField.parentElement.querySelector('.error-message:not([data-server-error])');
                        if (existingError) {
                            existingError.remove();
                        }

                        if (confirmPasswordField.value && passwordField.value !== confirmPasswordField.value) {
                            // Hiển thị lỗi
                            confirmPasswordField.classList.add('is-invalid');

                            // Tạo thông báo lỗi
                            const errorMessage = document.createElement('span');
                            errorMessage.className = 'error-message';
                            errorMessage.textContent = 'Mật khẩu xác nhận không khớp.';

                            // Thêm thông báo lỗi vào sau field
                            confirmPasswordField.parentElement.appendChild(errorMessage);
                        } else {
                            // Xóa class is-invalid
                            confirmPasswordField.classList.remove('is-invalid');
                        }
                    }

                    confirmPasswordField.addEventListener('input', checkPasswordMatch);
                    passwordField.addEventListener('input', checkPasswordMatch);
                }

                // Khởi tạo Flatpickr cho trường ngày sinh
                const dateField = document.getElementById('date_of_birth');
                if (dateField) {
                    flatpickr(dateField, {
                        dateFormat: "d/m/Y",
                        allowInput: true,
                        disableMobile: true,
                        altInput: true,
                        altFormat: "d/m/Y",
                        locale: {
                            firstDayOfWeek: 1, // Thứ 2 là ngày đầu tuần
                            weekdays: {
                                shorthand: ["CN", "T2", "T3", "T4", "T5", "T6", "T7"],
                                longhand: ["Chủ Nhật", "Thứ Hai", "Thứ Ba", "Thứ Tư", "Thứ Năm", "Thứ Sáu", "Thứ Bảy"]
                            },
                            months: {
                                shorthand: ["Th1", "Th2", "Th3", "Th4", "Th5", "Th6", "Th7", "Th8", "Th9", "Th10", "Th11", "Th12"],
                                longhand: ["Tháng Một", "Tháng Hai", "Tháng Ba", "Tháng Tư", "Tháng Năm", "Tháng Sáu", "Tháng Bảy", "Tháng Tám", "Tháng Chín", "Tháng Mười", "Tháng Mười Một", "Tháng Mười Hai"]
                            }
                        },
                        // Tùy chỉnh theme
                        monthSelectorType: "static",
                        yearSelectorType: "dropdown",
                        showMonths: 1,
                        time_24hr: true,
                        // Cho phép chọn năm trong khoảng rộng (1900-2023)
                        minDate: "01/01/1900",
                        maxDate: new Date().toISOString().split('T')[0],
                        // Đảm bảo ngày được định dạng đúng khi submit
                        onChange: function(selectedDates, dateStr, instance) {
                            if (selectedDates.length > 0) {
                                const formattedDate = selectedDates[0].toLocaleDateString('vi-VN', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric'
                                }).replace(/\./g, '/');
                                dateField.value = formattedDate;
                            }
                        },
                        // Tùy chỉnh màu sắc
                        onReady: function(selectedDates, dateStr, instance) {
                            // Thêm class cho calendar để tùy chỉnh màu sắc
                            setTimeout(function() {
                                const calendar = document.querySelector('.flatpickr-calendar');
                                if (calendar) {
                                    calendar.classList.add('medical-theme');
                                }

                                // Tạo dropdown năm với nhiều lựa chọn hơn
                                const yearInput = document.querySelector('.numInput.cur-year');
                                if (yearInput) {
                                    // Thay đổi sự kiện khi click vào năm
                                    yearInput.addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        const currentYear = new Date().getFullYear();
                                        const yearDropdown = document.createElement('select');
                                        yearDropdown.className = 'flatpickr-year-dropdown';
                                        yearDropdown.style.position = 'absolute';
                                        yearDropdown.style.top = '100%';
                                        yearDropdown.style.left = '0';
                                        yearDropdown.style.zIndex = '1000';
                                        yearDropdown.style.height = '200px';
                                        yearDropdown.style.overflow = 'auto';
                                        yearDropdown.style.backgroundColor = 'white';
                                        yearDropdown.style.border = '1px solid #ddd';
                                        yearDropdown.style.borderRadius = '4px';
                                        yearDropdown.style.padding = '5px';

                                        // Thêm các năm từ 1900 đến hiện tại
                                        for (let year = 1900; year <= currentYear; year++) {
                                            const option = document.createElement('option');
                                            option.value = year;
                                            option.textContent = year;
                                            if (year === instance.currentYear) {
                                                option.selected = true;
                                            }
                                            yearDropdown.appendChild(option);
                                        }

                                        // Xử lý khi chọn năm
                                        yearDropdown.addEventListener('change', function() {
                                            instance.currentYear = parseInt(this.value);
                                            instance.redraw();
                                            this.remove();
                                        });

                                        // Thêm dropdown vào DOM
                                        yearInput.parentNode.appendChild(yearDropdown);

                                        // Đóng dropdown khi click ra ngoài
                                        document.addEventListener('click', function closeDropdown(e) {
                                            if (!yearDropdown.contains(e.target) && e.target !== yearInput) {
                                                yearDropdown.remove();
                                                document.removeEventListener('click', closeDropdown);
                                            }
                                        });
                                    });
                                }
                            }, 100);
                        }
                    });
                }
            }
        });
    </script>
</body>

</html><?php /**PATH /var/www/html/resources/themes/medical/views/signup/signup.blade.php ENDPATH**/ ?>