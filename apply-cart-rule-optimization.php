<?php

/**
 * <PERSON><PERSON><PERSON> to apply Cart Rule Performance Optimizations
 * Run this script to automatically apply all optimizations
 */

echo "=== Cart Rule Performance Optimization Setup ===\n\n";

// 1. Check if running in Laravel environment
if (!function_exists('base_path')) {
    echo "❌ Error: This script must be run in Laravel environment\n";
    echo "Please run: php artisan optimize:cart-rules\n";
    exit(1);
}

// 2. Publish config file
echo "📝 Publishing configuration file...\n";
try {
    Artisan::call('vendor:publish', [
        '--tag' => 'cart-rule-config',
        '--force' => true
    ]);
    echo "✅ Configuration file published\n";
} catch (Exception $e) {
    echo "⚠️  Warning: Could not publish config file: " . $e->getMessage() . "\n";
}

// 3. Clear existing cache
echo "🧹 Clearing existing cache...\n";
try {
    Artisan::call('cart-rule:clear-cache');
    echo "✅ Cache cleared\n";
} catch (Exception $e) {
    echo "⚠️  Warning: Could not clear cache: " . $e->getMessage() . "\n";
}

// 4. Optimize application
echo "⚡ Optimizing application...\n";
try {
    Artisan::call('config:cache');
    Artisan::call('route:cache');
    echo "✅ Application optimized\n";
} catch (Exception $e) {
    echo "⚠️  Warning: Could not optimize application: " . $e->getMessage() . "\n";
}

// 5. Check database indexes
echo "🔍 Checking database indexes...\n";
$recommendations = [];

try {
    // Check categories table
    $categoriesCount = DB::table('categories')->count();
    if ($categoriesCount > 1000) {
        $recommendations[] = "Consider adding index on categories.parent_id and categories.position";
    }

    // Check attributes table
    $attributesCount = DB::table('attributes')->count();
    if ($attributesCount > 100) {
        $recommendations[] = "Consider adding index on attributes.type";
    }

    // Check attribute_options table
    $optionsCount = DB::table('attribute_options')->count();
    if ($optionsCount > 10000) {
        $recommendations[] = "Consider adding index on attribute_options.attribute_id and attribute_options.sort_order";
    }

    echo "✅ Database analysis complete\n";
    
    if (!empty($recommendations)) {
        echo "\n📊 Database Optimization Recommendations:\n";
        foreach ($recommendations as $recommendation) {
            echo "   • $recommendation\n";
        }
    }
} catch (Exception $e) {
    echo "⚠️  Warning: Could not analyze database: " . $e->getMessage() . "\n";
}

// 6. Test configuration
echo "\n🧪 Testing configuration...\n";
$config = config('cart-rule-optimization');
if ($config) {
    echo "✅ Configuration loaded successfully\n";
    echo "   • Cache enabled: " . ($config['cache']['enabled'] ? 'Yes' : 'No') . "\n";
    echo "   • Lazy loading enabled: " . ($config['lazy_loading']['enabled'] ? 'Yes' : 'No') . "\n";
    echo "   • Max execution time: " . $config['timeout']['max_execution_time'] . "s\n";
    echo "   • Memory limit: " . $config['timeout']['memory_limit'] . "\n";
} else {
    echo "❌ Configuration not found\n";
}

// 7. Environment variables check
echo "\n🔧 Environment Variables Check:\n";
$envVars = [
    'CART_RULE_CACHE_ENABLED' => env('CART_RULE_CACHE_ENABLED', 'not set'),
    'CART_RULE_LAZY_LOADING_ENABLED' => env('CART_RULE_LAZY_LOADING_ENABLED', 'not set'),
    'CART_RULE_MAX_EXECUTION_TIME' => env('CART_RULE_MAX_EXECUTION_TIME', 'not set'),
    'CART_RULE_MEMORY_LIMIT' => env('CART_RULE_MEMORY_LIMIT', 'not set'),
];

foreach ($envVars as $var => $value) {
    $status = $value !== 'not set' ? '✅' : '⚠️ ';
    echo "   $status $var: $value\n";
}

// 8. Performance test
echo "\n⏱️  Running performance test...\n";
try {
    $startTime = microtime(true);
    $startMemory = memory_get_usage();

    // Simulate loading condition attributes
    $cartRuleRepo = app('Webkul\CartRule\Repositories\CartRuleRepository');
    $attributes = $cartRuleRepo->getConditionAttributes();

    $endTime = microtime(true);
    $endMemory = memory_get_usage();

    $executionTime = round(($endTime - $startTime) * 1000, 2);
    $memoryUsed = round(($endMemory - $startMemory) / 1024 / 1024, 2);

    echo "✅ Performance test completed\n";
    echo "   • Execution time: {$executionTime}ms\n";
    echo "   • Memory used: {$memoryUsed}MB\n";
    echo "   • Attributes loaded: " . count($attributes) . "\n";

    if ($executionTime > 5000) {
        echo "⚠️  Warning: Execution time is high. Consider enabling more optimizations.\n";
    }
} catch (Exception $e) {
    echo "❌ Performance test failed: " . $e->getMessage() . "\n";
}

// 9. Final recommendations
echo "\n📋 Final Setup Steps:\n";
echo "1. Add the following to your .env file if not already present:\n";
echo "   CART_RULE_CACHE_ENABLED=true\n";
echo "   CART_RULE_LAZY_LOADING_ENABLED=true\n";
echo "   CART_RULE_MAX_EXECUTION_TIME=300\n";
echo "   CART_RULE_MEMORY_LIMIT=512M\n\n";

echo "2. Register the service provider in config/app.php:\n";
echo "   Webkul\\CartRule\\Providers\\CartRuleOptimizationServiceProvider::class,\n\n";

echo "3. Register the middleware in app/Http/Kernel.php:\n";
echo "   'optimize-cart-rule' => \\Webkul\\Admin\\Http\\Middleware\\OptimizeCartRuleTimeout::class,\n\n";

echo "4. Apply the middleware to cart rule routes in your route files.\n\n";

echo "5. Test the cart rule creation page: /admin/marketing/promotions/cart-rules/create\n\n";

echo "🎉 Cart Rule optimization setup completed!\n";
echo "📖 See CART_RULE_OPTIMIZATION_README.md for detailed documentation.\n";
