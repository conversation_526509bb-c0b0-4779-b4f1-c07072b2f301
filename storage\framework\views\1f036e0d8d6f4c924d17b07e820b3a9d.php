<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cart.css', 'resources/themes/medical/css/input-focus.css']); ?>
<style>
    .empty-cart {
        text-align: center;
        padding: 30px 0;
    }
    .no-products-message {
        color: #000;
        font-size: 18px;
        margin-bottom: 20px;
    }
    .remove-btn {
        background: none;
        border: none;
        color: #e74c3c;
        cursor: pointer;
        font-size: 16px;
    }
    .remove-btn:hover {
        color: #c0392b;
    }
    .continue-shopping-btn {
        color: #ff8c00;
        text-decoration: none;
        font-size: 16px;
    }
    .continue-shopping-btn:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .remove-btn{
            margin-right: 10px;
        }
    }
</style>
<?php echo $__env->make('medical::common.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<div class="cart-container">
    <div class="cart-main">
        <div class="cart-header">
            <h1 class="cart-title">Báo giá</h1>
            <button id="delete-selected" class="delete-btn" disabled>Xóa</button>
        </div>

        <div class="cart-table">
            <div class="cart-table-header">
                <div class="cart-checkbox-cell">
                    <input type="checkbox" id="select-all" class="cart-checkbox">
                    <label for="select-all">Sản phẩm</label>
                </div>
                <div class="cart-price-cell">Đơn giá</div>
                <div class="cart-quantity-cell">Số lượng</div>
                <div class="cart-total-cell">Thành tiền</div>
                <div class="cart-action-cell">Xóa sản phẩm</div>
            </div>

            <?php if($quoteItems->count() > 0): ?>
                <?php $__currentLoopData = $quoteItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="cart-item" data-id="<?php echo e($item->id); ?>">
                    <div class="cart-checkbox-cell">
                        <input type="checkbox" id="item-<?php echo e($item->id); ?>" class="cart-checkbox item-checkbox" data-id="<?php echo e($item->id); ?>">
                        <label for="item-<?php echo e($item->id); ?>" class="sr-only">Chọn sản phẩm</label>
                        <div class="cart-product-info">
                            <img src="<?php echo e($item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>" alt="<?php echo e($item->name); ?>" class="cart-product-image">
                            <div class="cart-product-details">
                                <h3 class="cart-product-name"><?php echo e($item->name); ?></h3>
                                <div class="cart-product-variant">
                                    <span class="variant-label">Phân loại: </span>
                                    <span class="selected-variant"><?php echo e($item->product->type ?? 'Mặc định'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cart-price-cell">
                        Liên hệ
                    </div>
                    <div class="cart-quantity-cell">
                        <div class="quantity-control">
                            <button class="quantity-btn minus" data-id="<?php echo e($item->id); ?>">-</button>
                            <input type="text" class="quantity-input quote" value="<?php echo e($item->quantity); ?>" min="1" data-id="<?php echo e($item->id); ?>">
                            <button class="quantity-btn plus" data-id="<?php echo e($item->id); ?>">+</button>
                        </div>
                    </div>
                    <div class="cart-total-cell">
                        Liên hệ
                    </div>
                    <div class="cart-action-cell">
                        <button class="remove-btn" data-id="<?php echo e($item->id); ?>"><i class="fa-solid fa-trash-can"></i></button>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="empty-cart">
                    <p class="no-products-message">Chưa có sản phẩm nào trong báo giá</p>
                    <a href="/" class="continue-shopping-btn"><strong>Quay lại trang chủ</strong></a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if($quoteItems->count() > 0): ?>
    <div class="cart-sidebar">
        <div class="cart-summary">
            <h2 class="summary-title">Thông tin báo giá</h2>
            <div class="summary-row">
                <span>Tổng sản phẩm:</span>
                <span id="total-items"><?php echo e($quoteCount); ?></span>
            </div>
            <button id="submit-quote" class="checkout-btn" disabled>Gửi yêu cầu báo giá</button>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Recently Viewed Products Section -->
<div class="recently-viewed-section mt-12">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Sản phẩm đã xem</h2>
            <a href="<?php echo e(route('recently_viewed')); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors inline-block" style="white-space: nowrap;">
                Xem thêm >
            </a>
        </div>

        <!-- Recently Viewed Products using Common Component -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            <?php $__empty_1 = true; $__currentLoopData = $recentlyViewedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php echo $__env->make('medical::common.product_card', [
                    'product' => $product
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">Chưa có sản phẩm nào được xem gần đây</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý checkbox chọn tất cả
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const deleteSelectedBtn = document.getElementById('delete-selected');
        const submitQuoteBtn = document.getElementById('submit-quote');

        // --- Mặc định check tất cả khi vào trang ---
        if (selectAllCheckbox) selectAllCheckbox.checked = true;
        itemCheckboxes.forEach(checkbox => checkbox.checked = true);

        // Cập nhật trạng thái các nút ngay khi load
        function updateButtonsState() {
            const checkedItems = document.querySelectorAll('.item-checkbox:checked');
            const count = checkedItems.length;
            const hasCheckedItems = count > 0;

            // Cập nhật nút xóa
            deleteSelectedBtn.disabled = !hasCheckedItems;

            // Cập nhật nút gửi yêu cầu báo giá
            if (submitQuoteBtn) {
                submitQuoteBtn.disabled = !hasCheckedItems;
                let text = 'Gửi yêu cầu báo giá';
                if (count > 0) {
                    text += ` (${count})`;
                }
                submitQuoteBtn.innerText = text;
            }
        }
        updateButtonsState();

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateButtonsState();
            });
        }

        // Xử lý checkbox từng sản phẩm
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateButtonsState);
        });

        // Xử lý nút xóa nhiều sản phẩm
        deleteSelectedBtn.addEventListener('click', function() {
            const checkedItems = document.querySelectorAll('.item-checkbox:checked');
            const itemIds = Array.from(checkedItems).map(checkbox => checkbox.getAttribute('data-id'));

            if (itemIds.length > 0) {
                // Hiển thị modal xác nhận
                ModalCommon.showConfirm(
                    `Bạn có muốn xóa ${itemIds.length} sản phẩm đã chọn khỏi báo giá?`,
                    function() {
                        removeMultipleItems(itemIds);
                    }
                );
            }
        });

        // Xử lý nút xóa từng sản phẩm
        const removeButtons = document.querySelectorAll('.remove-btn');
        removeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                // Lấy tên sản phẩm để hiển thị trong modal
                const cartItem = document.querySelector(`.cart-item[data-id="${itemId}"]`);
                const productName = cartItem ? cartItem.querySelector('.cart-product-name').textContent : 'sản phẩm này';

                // Hiển thị modal xác nhận
                ModalCommon.showConfirm(
                    `Bạn có muốn xóa sản phẩm "${productName}" khỏi báo giá?`,
                    function() {
                        removeItem(itemId);
                    }
                );
            });
        });

        // Xử lý nút tăng/giảm số lượng
        const minusButtons = document.querySelectorAll('.quantity-btn.minus');
        const plusButtons = document.querySelectorAll('.quantity-btn.plus');
        const quantityInputs = document.querySelectorAll('.quantity-input');

        minusButtons.forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                const input = document.querySelector(`.quantity-input[data-id="${itemId}"]`);
                let value = parseInt(input.value);
                if (value > 1) {
                    input.value = value - 1;
                    updateQuantity(itemId, value - 1);
                } else {
                    // Hiển thị modal xác nhận khi số lượng là 1 và ấn nút trừ
                    const cartItem = document.querySelector(`.cart-item[data-id="${itemId}"]`);
                    const productName = cartItem.querySelector('.cart-product-name').textContent;
                    ModalCommon.showConfirm(
                        `Bạn có muốn xóa sản phẩm "${productName}" khỏi báo giá?`,
                        function() {
                            removeItem(itemId);
                        }
                    );
                }
            });
        });

        plusButtons.forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-id');
                const input = document.querySelector(`.quantity-input[data-id="${itemId}"]`);
                let value = parseInt(input.value);
                input.value = value + 1;
                updateQuantity(itemId, value + 1);
            });
        });

        quantityInputs.forEach(input => {
            input.addEventListener('change', function() {
                const itemId = this.getAttribute('data-id');
                let value = parseInt(this.value);
                if (value < 1) {
                    value = 1;
                    this.value = 1;
                }
                updateQuantity(itemId, value);
            });
        });

        // Xử lý nút gửi yêu cầu báo giá
        if (submitQuoteBtn) {
            submitQuoteBtn.addEventListener('click', function() {
                submitQuote('');
            });

            // Cập nhật trạng thái ban đầu của nút
            updateButtonsState();
        }

        // Hàm xóa một sản phẩm
        function removeItem(itemId) {
            fetch('/api/medical/quote/remove-item', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hiển thị toast thành công
                    Toastify({
                        text: "Đã xóa sản phẩm khỏi báo giá thành công",
                        duration: 2000,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#28a745"
                    }).showToast();

                    // Cập nhật UI
                    const itemElement = document.querySelector(`.cart-item[data-id="${itemId}"]`);
                    if (itemElement) {
                        itemElement.remove();
                    }

                    // Cập nhật số lượng cục bộ
                    document.getElementById('total-items').textContent = data.quote_count;

                    // Cập nhật số lượng trên header
                    if (typeof updateHeaderQuoteCount === 'function') {
                        updateHeaderQuoteCount();
                    }

                    // Cập nhật trạng thái nút
                    updateButtonsState();

                    // Kiểm tra nếu không còn sản phẩm nào
                    if (data.quote_count === 0) {
                        location.reload(); // Tải lại trang để hiển thị giỏ hàng trống
                    }
                } else {
                    Toastify({
                        text: data.message || "Có lỗi xảy ra khi xóa sản phẩm",
                        duration: 2000,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#e74c3c"
                    }).showToast();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Toastify({
                    text: 'Đã xảy ra lỗi khi xóa sản phẩm1',
                    duration: 1500,
                    gravity: "top",
                    position: "right",
                    close: true,
                    backgroundColor: "#e74c3c"
                }).showToast();
            });
        }

        // Hàm xóa nhiều sản phẩm
        function removeMultipleItems(itemIds) {
            // Xóa từng sản phẩm một
            let promises = itemIds.map(itemId => {
                return fetch('/api/medical/quote/remove-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        item_id: itemId
                    })
                }).then(response => response.json());
            });

            Promise.all(promises)
                .then(results => {
                    // Lấy kết quả cuối cùng để cập nhật số lượng
                    const lastResult = results[results.length - 1];

                    // Hiển thị toast thành công
                    const deletedCount = itemIds.length;
                    Toastify({
                        text: `Đã xóa ${deletedCount} sản phẩm khỏi báo giá thành công`,
                        duration: 2000,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#28a745"
                    }).showToast();

                    // Xóa các phần tử khỏi DOM
                    itemIds.forEach(itemId => {
                        const itemElement = document.querySelector(`.cart-item[data-id="${itemId}"]`);
                        if (itemElement) {
                            itemElement.remove();
                        }
                    });

                    // Cập nhật số lượng cục bộ
                    document.getElementById('total-items').textContent = lastResult.quote_count;

                    // Cập nhật số lượng trên header
                    if (typeof updateHeaderQuoteCount === 'function') {
                        updateHeaderQuoteCount();
                    }

                    // Cập nhật trạng thái nút
                    updateButtonsState();

                    // Kiểm tra nếu không còn sản phẩm nào
                    if (lastResult.quote_count === 0) {
                        location.reload(); // Tải lại trang để hiển thị giỏ hàng trống
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toastify({
                        text: 'Đã xảy ra lỗi khi xóa sản phẩm',
                        duration: 1500,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#e74c3c"
                    }).showToast();
                });
        }

        // Hàm cập nhật số lượng
        function updateQuantity(itemId, quantity) {
            fetch('/api/medical/quote/update-quantity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                // Không hiển thị thông báo khi cập nhật số lượng
            })
            .catch(error => {
                console.error('Error:', error);
                Toastify({
                    text: 'Đã xảy ra lỗi khi cập nhật số lượng',
                    duration: 1500,
                    gravity: "top",
                    position: "right",
                    close: true,
                    backgroundColor: "#e74c3c"
                }).showToast();
            });
        }

        // Hàm gửi yêu cầu báo giá
        function submitQuote(note) {
            // Lấy danh sách ID của các sản phẩm được chọn
            const checkedItems = document.querySelectorAll('.item-checkbox:checked');
            const selectedItems = Array.from(checkedItems).map(checkbox => checkbox.getAttribute('data-id'));

            fetch('/api/medical/quote/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    selected_items: selectedItems,
                    note: note
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/profile/cost';
                    Toastify({
                        text: data.message,
                        duration: 1300,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#4CAF50"
                    }).showToast();

                    // Redirect to My Quotes page after toast finishes
                    setTimeout(() => {
                        window.location.href = data.redirect_url || '/profile/cost';
                    }, 1200);
                } else {
                    Toastify({
                        text: data.message,
                        duration: 1500,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#e74c3c"
                    }).showToast();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Toastify({
                    text: 'Đã xảy ra lỗi khi gửi yêu cầu báo giá',
                    duration: 1500,
                    gravity: "top",
                    position: "right",
                    close: true,
                    backgroundColor: "#e74c3c"
                }).showToast();
            });
        }

        function confirmDeleteItem() {
            if (currentDeleteItemId) {
                fetch(`/admin/sales/quotes/items/${currentDeleteItemId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hiển thị toast thành công
                        Toastify({
                            text: "Đã xóa sản phẩm khỏi báo giá thành công",
                            duration: 2000,
                            gravity: "top",
                            position: "right",
                            close: true,
                            backgroundColor: "#28a745"
                        }).showToast();

                        // Xóa dòng item khỏi bảng
                        const row = document.querySelector(`tr[data-item-id="${currentDeleteItemId}"]`);
                        if (row) row.remove();

                        // Cập nhật lại số lượng sản phẩm cục bộ
                        const countSpan = document.querySelector('.quote-items-count');
                        if (countSpan) {
                            let count = document.querySelectorAll('tr[data-item-id]').length;
                            countSpan.textContent = count;
                        }

                        // Cập nhật số lượng trên header
                        if (typeof updateHeaderQuoteCount === 'function') {
                            updateHeaderQuoteCount();
                        }

                        // Nếu không còn item nào, chuyển trạng thái về cancelled
                        if (document.querySelectorAll('tr[data-item-id]').length === 0) {
                            // Gọi API chuyển trạng thái quote về cancelled
                            fetch(`/admin/sales/quotes/${currentQuoteId}/cancel`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Reload lại trang để cập nhật trạng thái
                                    window.location.reload();
                                } else {
                                    Toastify({
                                        text: "Có lỗi xảy ra khi cập nhật trạng thái báo giá",
                                        duration: 2000,
                                        gravity: "top",
                                        position: "right",
                                        close: true,
                                        backgroundColor: "#e74c3c"
                                    }).showToast();
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                Toastify({
                                    text: "Có lỗi xảy ra khi cập nhật trạng thái báo giá",
                                    duration: 2000,
                                    gravity: "top",
                                    position: "right",
                                    close: true,
                                    backgroundColor: "#e74c3c"
                                }).showToast();
                            });
                        }
                    } else {
                        Toastify({
                            text: "Có lỗi xảy ra khi xóa sản phẩm",
                            duration: 2000,
                            gravity: "top",
                            position: "right",
                            close: true,
                            backgroundColor: "#e74c3c"
                        }).showToast();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toastify({
                        text: "Có lỗi xảy ra khi xóa sản phẩm",
                        duration: 2000,
                        gravity: "top",
                        position: "right",
                        close: true,
                        backgroundColor: "#e74c3c"
                    }).showToast();
                });
            }
            closeDeleteItemModal();
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/quote/index.blade.php ENDPATH**/ ?>