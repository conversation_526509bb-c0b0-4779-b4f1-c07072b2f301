<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/quick_order.css', 'resources/themes/medical/css/input-focus.css']); ?>

<div class="quick-order-container">
    <h1 class="quick-order-title">Đặt hàng nhanh</h1>
    <div class="quick-order-search-section">
        <label class="quick-order-search-label">Tìm kiếm thuốc</label>
        <div class="quick-order-search-bar">
            <input type="text" class="quick-order-search-input" placeholder="Nhậ<PERSON> tên thuốc, hoạt chất, công dụng...">
            <button class="quick-order-search-btn">Tìm kiếm</button>
        </div>
    </div>
    <div class="quick-order-content">
        <!-- Sidebar -->
        <div class="quick-order-sidebar">
            <div class="quick-order-filter">
                <div class="quick-order-filter-title"><PERSON>h mục</div>
                <div class="quick-order-filter-list scrollable-list">
                    <label><input type="checkbox"> Thuốc kê đơn</label>
                    <label><input type="checkbox"> Thuốc không kê đơn</label>
                    <label><input type="checkbox"> Thực phẩm chức năng</label>
                    <label><input type="checkbox"> Thiết bị y tế</label>
                    <label><input type="checkbox"> Dụng cụ y tế</label>
                    <label><input type="checkbox"> Vật tư tiêu hao</label>
                    <label><input type="checkbox"> Dược mỹ phẩm</label>
                    <label><input type="checkbox"> Khác</label>
                </div>
            </div>
            <div class="quick-order-filter">
                <div class="quick-order-filter-title">Thương hiệu</div>
                <div class="quick-order-filter-list scrollable-list">
                    <label><input type="checkbox"> Thương hiệu 1</label>
                    <label><input type="checkbox"> Thương hiệu 2</label>
                    <label><input type="checkbox"> Thương hiệu 3</label>
                    <label><input type="checkbox"> Thương hiệu 4</label>
                    <label><input type="checkbox"> Thương hiệu 5</label>
                    <label><input type="checkbox"> Thương hiệu 6</label>
                    <label><input type="checkbox"> Thương hiệu 7</label>
                    <label><input type="checkbox"> Thương hiệu 8</label>
                </div>
            </div>
        </div>
        <!-- Product List -->
        <div class="quick-order-products">
            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

            <div class="quick-order-product-card" title="<?php echo e($product->name); ?>">
                <a href="<?php echo e(route('product_detail', ['productId' => $product->id])); ?>" title="<?php echo e($product->name); ?>">
                    <div class="quick-order-product-img">
                        <img src="<?php echo e($product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png')); ?>" alt="<?php echo e($product->name); ?>">
                        <?php if(isset($product->discount) && $product->discount > 0 && $product->visible_price && Auth::guard('customer')->check()): ?>
                        <span class="discount-badge">-<?php echo e($product->discount); ?>%</span>
                        <?php endif; ?>
                    </div>
                    <div class="quick-order-product-name"><?php echo e($product->name); ?></div>
                    <div class="quick-order-product-brand">Thương hiệu: <?php echo e($product->brand ?? 'N/A'); ?></div>
                    <div class="quick-order-product-unit">Đơn vị: <?php echo e($product->unit ?? 'N/A'); ?></div>
                    <div class="quick-order-product-price">
                        <?php if($product->visible_price): ?>
                        <?php if(isset($product->has_discount) && $product->has_discount): ?>
                        <span class="price-old"><?php echo e(number_format($product->original_price, 0, ',', '.')); ?>đ</span>
                        <span class="price-new"><?php echo e(number_format($product->price, 0, ',', '.')); ?>đ</span>
                        <?php else: ?>
                        <span class="price-new"><?php echo e(number_format($product->price, 0, ',', '.')); ?>đ</span>
                        <?php endif; ?>
                        <?php else: ?>
                        <span class="price-new">Liên hệ</span>
                        <?php endif; ?>
                    </div>
                </a>
                <input type="number" class="quick-order-qty-input" name="quantity_<?php echo e($product->id); ?>" min="1" value="1" style="width: 60px; margin-bottom: 8px; text-align: center;" />
                <button class="quick-order-buy-btn" data-product-id="<?php echo e($product->id); ?>">Đặt ngay</button>
            </div>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
        </div>
         
    </div>
    <?php echo e($products->links('vendor.pagination.custom')); ?>

</div>
<script>

document.addEventListener('DOMContentLoaded', function() {
    // Tìm tất cả input số lượng
    document.querySelectorAll('.quick-order-qty-input').forEach(function(input) {
        // Tạo wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'quick-order-qty-wrapper';

        // Tạo nút -
        const btnMinus = document.createElement('button');
        btnMinus.type = 'button';
        btnMinus.className = 'quick-order-qty-btn quick-order-qty-btn-minus';
        btnMinus.textContent = '−';

        // Tạo nút +
        const btnPlus = document.createElement('button');
        btnPlus.type = 'button';
        btnPlus.className = 'quick-order-qty-btn quick-order-qty-btn-plus';
        btnPlus.textContent = '+';

        // Đưa input vào wrapper
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(btnMinus);
        wrapper.appendChild(input);
        wrapper.appendChild(btnPlus);

        // Xử lý sự kiện
        btnMinus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            if (val > (parseInt(input.min) || 1)) {
                input.value = val - 1;
                input.dispatchEvent(new Event('change'));
            }
        });
        btnPlus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            let max = input.max ? parseInt(input.max) : Infinity;
            if (val < max) {
                input.value = val + 1;
                input.dispatchEvent(new Event('change'));
            }
        });
    });
});

    document.addEventListener('DOMContentLoaded', function() {
        // Xử lý phân trang
        document.querySelectorAll('.quick-order-pagination').forEach(function(pagination) {
            pagination.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-order-page-btn')) {
                    const buttons = Array.from(pagination.querySelectorAll('.quick-order-page-btn'))
                        .filter(btn => !btn.hasAttribute('data-page'));
                    // Nếu bấm <<
                    if (e.target.getAttribute('data-page') === 'first') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[0].classList.add('active');
                    }
                    // Nếu bấm >>
                    else if (e.target.getAttribute('data-page') === 'last') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[buttons.length - 1].classList.add('active');
                    }
                    // Nếu bấm số trang
                    else {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');
                    }
                }
            });
        });

        /**
         * Hiển thị thông báo
         * @param {string} message - Nội dung thông báo
         * @param {string} type - Loại thông báo (success, error, info)
         */
        function showToast(message, type) {
            // Kiểm tra xem Toastify đã được định nghĩa chưa
            if (typeof Toastify === 'function') {
                Toastify({
                    text: message,
                    duration: 1500, // Giảm thời gian hiển thị xuống 1.5 giây
                    gravity: "top",
                    position: "right",
                    backgroundColor: type === 'success' ? "#4CAF50" : type === 'error' ? "#F44336" : "#2196F3",
                    close: true
                }).showToast();
            } else {
                // Fallback nếu không có Toastify
                alert(message);
            }
        }

        // Xử lý chức năng thêm vào giỏ hàng
        function addToCart(productId, quantity) {
            if (!productId) {
                Toastify({
                    text: "Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.",
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                    close: true
                }).showToast();
                return;
            }

            // Gọi API thêm vào giỏ hàng
            fetch('/api/medical/cart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        quantity: quantity,
                        _token: '<?php echo e(csrf_token()); ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hiển thị thông báo thành công
                        Toastify({
                            text: data.message,
                            duration: 3000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#059669",
                            close: true
                        }).showToast();

                        // Cập nhật số lượng sản phẩm trong giỏ hàng (nếu có)
                        if (data.cart_count) {
                            const cartCountElement = document.querySelector('.cart-count');
                            if (cartCountElement) {
                                cartCountElement.textContent = data.cart_count;
                            }
                        }
                    } else {
                        // Hiển thị thông báo lỗi
                        Toastify({
                            text: data.message,
                            duration: 3000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: "#EF4444",
                            close: true
                        }).showToast();

                        // Nếu cần chuyển hướng đến trang đăng nhập
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toastify({
                        text: "Có lỗi xảy ra khi thêm vào giỏ hàng. Vui lòng thử lại sau.",
                        duration: 3000,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#EF4444",
                        close: true
                    }).showToast();
                })
                .finally(() => {

                });
        }

        document.querySelectorAll('.quick-order-buy-btn').forEach(function(button) {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();

                const productId = this.getAttribute('data-product-id');
                // Lấy input số lượng cùng card
                const qtyInput = this.closest('.quick-order-product-card').querySelector('.quick-order-qty-input');
                const quantity = qtyInput ? parseInt(qtyInput.value) : 1;

                // Gọi hàm addToCart với số lượng lấy được
                addToCart(productId, quantity);
            });
        });

        const searchInput = document.querySelector('.quick-order-search-input');
        const searchBtn = document.querySelector('.quick-order-search-btn');

        searchBtn.addEventListener('click', function() {
            if (searchInput.value.length >= 2) {
                // Chuyển hướng với params search
                window.location.href = `/quickorder?search=${encodeURIComponent(searchInput.value)}`;
            }
        });

        // Xử lý khi nhấn Enter trong ô search
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && this.value.length >= 2) {
                window.location.href = `/quickorder?search=${encodeURIComponent(this.value)}`;
            }
        });

        // Nếu có params search thì điền vào ô input
        const urlParams = new URLSearchParams(window.location.search);
        const searchParam = urlParams.get('search');
        if (searchParam) {
            searchInput.value = searchParam;
        }
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/quick_order/quick_order.blade.php ENDPATH**/ ?>