<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/quick_order.css', 'resources/themes/medical/css/input-focus.css']); ?>

<style>
/* Custom styles for toast notifications */
.toast-notification {
    font-family: 'Be Vietnam Pro', sans-serif !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.toast-notification .toast-close {
    color: white !important;
    opacity: 0.8 !important;
}

.toast-notification .toast-close:hover {
    opacity: 1 !important;
}

/* Fix cart count alignment */
.cart-count, .quote-count {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    line-height: 1 !important;
}

/* Login Required Styles */
.login-required-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 40px 20px;
}

.login-required-message {
    text-align: center;
    background: white;
    padding: 48px 32px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-width: 400px;
    width: 100%;
}

.login-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f97316;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s;
}

.login-btn:hover {
    background: #ea580c;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);
}

.login-btn i {
    font-size: 16px;
}
</style>

<?php echo $__env->make('medical::common.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="quick-order-container">
    <!-- Header với tìm kiếm -->
    <div class="quick-order-header">
        <div class="quick-order-search-section">
            <form class="quick-order-search-form" method="GET" action="<?php echo e(route('quick_order')); ?>">
                <div class="quick-order-search-bar">
                    <i class="fa-solid fa-search search-icon"></i>
                    <input type="text" name="search" class="quick-order-search-input" placeholder="Nhập tên thuốc, hoạt chất cần tìm" value="<?php echo e(request('search')); ?>">
                    <?php if(request('search')): ?>
                        <button type="button" class="quick-order-clear-btn" onclick="clearSearch()">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    <?php endif; ?>
                    <button type="submit" class="quick-order-search-btn">
                        <i class="fa-solid fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <?php if(Auth::guard('customer')->check()): ?>
    <!-- Main content with table and summary side by side -->
    <div class="quick-order-main">
        <!-- Left side: Product table -->
        <div class="quick-order-left">
            <div class="quick-order-table">
                <div class="quick-order-table-header">
                    <div class="quick-order-checkbox-cell">
                        <input type="checkbox" id="select-all-quick" class="quick-order-checkbox">
                        <label for="select-all-quick">Sản phẩm</label>
                    </div>
                    <div class="quick-order-price-cell">Đơn giá</div>
                    <div class="quick-order-status-cell">Trạng thái</div>
                    <div class="quick-order-quantity-cell">Số lượng</div>
                    <div class="quick-order-total-cell">Thành tiền</div>
                </div>

        <!-- Thông báo sắp xếp -->
        <div class="sort-info">
            <i class="fa-solid fa-info-circle"></i>
            <span>Sản phẩm được sắp xếp ưu tiên theo tình trạng còn hàng. Sản phẩm "Báo giá" sẽ được thêm vào danh sách báo giá khi nhấn +</span>
        </div>



        <div class="quick-order-items">
            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php
                $stock = 0;
                try {
                    $stock = $product->totalQuantity() ?? 0;
                } catch (\Exception $e) {
                    $stock = 0;
                }
            ?>
            <div class="quick-order-item <?php echo e($stock > 0 ? 'in-stock' : 'out-of-stock'); ?> <?php echo e(!$product->visible_price ? 'quote-product' : ''); ?>" data-id="<?php echo e($product->id); ?>" data-price="<?php echo e($product->price ?? 0); ?>" data-stock="<?php echo e($stock); ?>" data-is-quote="<?php echo e(!$product->visible_price ? 'true' : 'false'); ?>">
                <div class="quick-order-checkbox-cell">
                    <input type="checkbox" id="quick-item-<?php echo e($product->id); ?>" class="quick-order-checkbox item-checkbox">
                    <label for="quick-item-<?php echo e($product->id); ?>" class="sr-only">Chọn sản phẩm</label>
                    <div class="quick-order-product-info">
                        <img src="<?php echo e($product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png')); ?>" alt="<?php echo e($product->name); ?>" class="quick-order-product-image">
                        <div class="quick-order-product-details">
                            <h3 class="quick-order-product-name">
                                <?php echo e($product->name); ?>

                            </h3>
                            <div class="quick-order-product-meta">
                                <span class="product-brand"><?php echo e($product->brand ?? 'N/A'); ?></span>
                                <?php if(isset($product->discount) && $product->discount > 0 && $product->visible_price && Auth::guard('customer')->check()): ?>
                                <span class="discount-badge">-<?php echo e($product->discount); ?>%</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quick-order-price-cell">
                    <?php if($product->visible_price): ?>
                        <?php if(isset($product->has_discount) && $product->has_discount): ?>
                        <span class="price-old"><?php echo e(number_format($product->original_price, 0, ',', '.')); ?>đ</span>
                        <span class="product-price"><?php echo e(number_format($product->price, 0, ',', '.')); ?>đ</span>
                        <?php else: ?>
                        <span class="product-price"><?php echo e(number_format($product->price, 0, ',', '.')); ?>đ</span>
                        <?php endif; ?>
                    <?php else: ?>
                    <span class="product-price">Liên hệ</span>
                    <?php endif; ?>
                </div>
                <div class="quick-order-status-cell">
                    <?php if($stock > 0): ?>
                        <span class="status-badge status-in-stock">
                            <i class="fa-solid fa-check-circle"></i>
                            Còn hàng
                        </span>
                        <div class="stock-quantity"><?php echo e($stock); ?> sản phẩm</div>
                    <?php else: ?>
                        <span class="status-badge status-out-of-stock">
                            <i class="fa-solid fa-times-circle"></i>
                            Hết hàng
                        </span>
                    <?php endif; ?>
                </div>
                <div class="quick-order-quantity-cell">
                    <div class="quantity-control">
                        <button class="quantity-btn decrease">−</button>
                        <input type="text" class="quantity-input" value="0" readonly>
                        <button class="quantity-btn increase" <?php echo e((!$product->visible_price || $stock > 0) ? '' : 'disabled'); ?>>+</button>
                    </div>
                </div>
                <div class="quick-order-total-cell">
                    <span class="product-total">0đ</span>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="empty-quick-order">
                <p class="no-products-message">Không tìm thấy sản phẩm nào</p>
                <a href="/" class="continue-shopping-btn"><strong>Về trang chủ</strong></a>
            </div>
            <?php endif; ?>
        </div>
            </div>
        </div>

        <!-- Right side: Summary sidebar -->
        <div class="quick-order-right">
            <div class="quick-order-summary">
                <div class="summary-header">
                    <h2 class="summary-title">Giỏ Hàng</h2>
                </div>

                <div class="summary-content">
                    <div class="summary-row">
                        <span class="summary-label">Số lượng</span>
                        <span class="summary-count" id="summaryCount">0</span>
                    </div>

                    <div class="summary-total">
                        <span class="total-label">Tạm tính</span>
                        <span class="total-value" id="summaryTotal">0đ</span>
                    </div>

                    <a href="<?php echo e(route('cart')); ?>" class="checkout-btn">
                        <i class="fa-solid fa-shopping-cart"></i> XEM GIỎ HÀNG
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Pagination -->
    <?php echo e($products->links('vendor.pagination.custom')); ?>

    <?php else: ?>
        Đăng nhập để sử dụng chức năng
    <?php endif; ?>
</div>

<script>
// LocalStorage để giữ trạng thái qua các trang
const QUICK_ORDER_STORAGE_KEY = 'quick_order_data';

// Flag để tránh multiple initialization
let isQuickOrderInitialized = false;

// Dữ liệu Cart và Quote từ server
const cartQuantities = <?php echo json_encode($cartQuantities ?? [], 15, 512) ?>;
const quoteQuantities = <?php echo json_encode($quoteQuantities ?? [], 15, 512) ?>;

// Debug: Log dữ liệu Cart và Quote
console.log('Cart quantities:', cartQuantities);
console.log('Quote quantities:', quoteQuantities);

document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const quickOrderItems = document.querySelectorAll('.quick-order-item');
    const selectAllQuick = document.getElementById('select-all-quick');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');

    // ✅ SYNC QUANTITY TỪ SERVER DATA VÀO INPUT
    function syncQuantityFromServer() {
        console.log('🔄 Syncing quantities from server...');
        console.log('📊 Available Cart quantities:', cartQuantities);
        console.log('📊 Available Quote quantities:', quoteQuantities);

        document.querySelectorAll('.quick-order-item').forEach(item => {
            const productId = parseInt(item.dataset.id);
            const productName = item.querySelector('.quick-order-product-name')?.textContent?.trim() || 'Unknown';
            const quantityInput = item.querySelector('.quantity-input');
            const checkbox = item.querySelector('.item-checkbox');

            console.log(`🔍 Checking product: "${productName}" (ID: ${productId})`);

            if (!quantityInput || !checkbox) {
                console.log(`❌ Missing input/checkbox for product ${productId}`);
                return;
            }

            let serverQuantity = 0;
            let source = '';

            // Ưu tiên Quote trước, sau đó mới đến Cart
            if (quoteQuantities[productId]) {
                serverQuantity = quoteQuantities[productId];
                source = 'Quote';
                console.log(`📋 Product ${productId} (${productName}): Found in Quote = ${serverQuantity}`);
            } else if (cartQuantities[productId]) {
                serverQuantity = cartQuantities[productId];
                source = 'Cart';
                console.log(`🛒 Product ${productId} (${productName}): Found in Cart = ${serverQuantity}`);
            } else {
                console.log(`⚪ Product ${productId} (${productName}): Not found in Cart or Quote`);
            }

            if (serverQuantity > 0) {
                quantityInput.value = serverQuantity;
                checkbox.checked = true;

                // Cập nhật tổng tiền cho item này
                updateItemTotal(item, serverQuantity);

                console.log(`✅ Product ${productId} (${productName}): Set quantity = ${serverQuantity} from ${source}`);
            }
        });
    }

    // Gọi sync ngay khi DOM load
    syncQuantityFromServer();

    // Khởi tạo tổng tiền ban đầu
    updateSelectedItems();

    // ✅ DEBOUNCE FUNCTION (COPY TỪ CART)
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    // 2. Xử lý check/uncheck "Sản phẩm" (select all)
    function updateSelectAllCheckbox() {
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (checkedCount === 0) {
            selectAllQuick.checked = false;
            selectAllQuick.indeterminate = false;
        } else if (checkedCount === currentItemCheckboxes.length) {
            selectAllQuick.checked = true;
            selectAllQuick.indeterminate = false;
        } else {
            selectAllQuick.checked = false;
            selectAllQuick.indeterminate = true;
        }
    }



    // 4. Cập nhật tổng tiền và danh sách sản phẩm được chọn
    function updateSelectedItems() {
        const selectedItems = [];
        let total = 0;
        let count = 0;

        // Query lại từ DOM thay vì sử dụng NodeList cũ
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');

        currentItemCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const itemElement = checkbox.closest('.quick-order-item');
                if (itemElement) { // Kiểm tra element còn tồn tại
                    const itemId = itemElement.dataset.id;
                    const price = parseFloat(itemElement.dataset.price);
                    const quantity = parseInt(itemElement.querySelector('.quantity-input').value);
                    const isQuote = itemElement.dataset.isQuote === 'true';

                    // Chỉ thêm vào danh sách nếu số lượng > 0
                    if (quantity > 0) {
                        selectedItems.push(itemId);

                        // Chỉ cộng tiền nếu KHÔNG phải sản phẩm báo giá
                        if (!isQuote) {
                            total += price * quantity;
                        }

                        count += quantity;
                    }
                }
            }
        });

        // Không cần lưu vào hidden input nữa vì không dùng form

        // Cập nhật tổng tiền và số lượng CHỈ cho trang hiện tại
        // Không ghi đè dữ liệu từ localStorage
        try {
            const currentPageTotal = document.querySelector('.total-value');
            const currentPageCount = document.querySelector('.summary-count');

            // Chỉ cập nhật nếu không có dữ liệu localStorage hoặc đang ở chế độ local
            const savedData = localStorage.getItem(QUICK_ORDER_STORAGE_KEY);
            let hasStorageData = false;

            if (savedData) {
                try {
                    const parsedData = JSON.parse(savedData);
                    hasStorageData = Object.keys(parsedData.products || {}).length > 0;
                } catch (e) {
                    console.error('Error parsing localStorage data:', e);
                }
            }

            if (!hasStorageData) {
                if (currentPageTotal) currentPageTotal.textContent = formatPrice(total);
                if (currentPageCount) currentPageCount.textContent = count;
            }
        } catch (error) {
            console.error('Error in updateSelectedItems:', error);
        }

        updateSelectAllCheckbox();
    }

    // 5. Sự kiện tick sản phẩm
    const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
    currentItemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedItems);
    });

    // 6. Sự kiện tick "Sản phẩm" (select all)
    selectAllQuick.addEventListener('change', function() {
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
        currentItemCheckboxes.forEach(cb => cb.checked = selectAllQuick.checked);
        updateSelectedItems();
    });

    // Hàm format giá
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price).replace('VND', 'đ');
    }

    // Debounce để tránh lặp toast
    let lastToastTime = 0;
    let lastToastMessage = '';

    // Hàm hiển thị toast notification
    function showToast(toast) {
        const now = Date.now();
        const message = toast.message;

        // Tránh hiển thị cùng một message trong vòng 1 giây
        if (now - lastToastTime < 1000 && message === lastToastMessage) {
            return;
        }

        lastToastTime = now;
        lastToastMessage = message;

        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: toast.type === 'success' ? '#28a745' :
                               toast.type === 'warning' ? '#ffc107' :
                               toast.type === 'info' ? '#17a2b8' : '#dc3545',
                className: "toast-notification",
                close: true,
                stopOnFocus: true
            }).showToast();
        } else {
            // Fallback nếu không có Toastify
            alert(message);
        }
    }
    // Hàm cập nhật số lượng giỏ hàng ở header
    function updateHeaderCartCount(cartCount = null) {
        // Cập nhật số lượng ở header
        const cartCountElements = document.querySelectorAll('.cart-count, [data-cart-count]');
        cartCountElements.forEach(element => {
            element.textContent = cartCount;
            if (cartCount > 0) {
                element.style.display = '';
                element.style.visibility = 'visible';
            } else {
                element.style.visibility = 'hidden';
            }
        });
    }

    // 8. Hàm cập nhật tổng tiền của 1 item
    function updateItemTotal(quickOrderItem, quantity) {
        const isQuote = quickOrderItem.dataset.isQuote === 'true';
        const totalElement = quickOrderItem.querySelector('.product-total');

        if (totalElement) {
            if (isQuote && quantity > 0) {
                // Sản phẩm báo giá - hiển thị "Báo giá"
                totalElement.textContent = 'Báo giá';
                totalElement.style.color = '#ff6b00';
                totalElement.style.fontWeight = 'bold';
            } else {
                // Sản phẩm thường - tính tiền bình thường
                const price = parseFloat(quickOrderItem.dataset.price) || 0;
                const total = price * quantity;
                totalElement.textContent = formatPrice(total);
                totalElement.style.color = '';
                totalElement.style.fontWeight = '';
            }
        }

        // Cập nhật trạng thái nút increase dựa trên stock
        const stock = parseInt(quickOrderItem.dataset.stock) || 0;
        const increaseBtn = quickOrderItem.querySelector('.quantity-btn.increase');
        if (increaseBtn) {
            increaseBtn.disabled = (quantity >= stock || stock <= 0);
        }
    }

    // ✅ XỬ LÝ NÚT +/- (COPY TỪ CART PAGE)
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        // Tránh duplicate event listeners
        if (btn.hasAttribute('data-listener-attached')) {
            return;
        }
        btn.setAttribute('data-listener-attached', 'true');

        btn.addEventListener('click', function(e) {
            e.preventDefault();

            const quickOrderItem = btn.closest('.quick-order-item');
            const input = quickOrderItem.querySelector('.quantity-input');
            const checkbox = quickOrderItem.querySelector('.item-checkbox');
            const productId = quickOrderItem.dataset.id;
            const productName = quickOrderItem.querySelector('.quick-order-product-name').textContent.trim();
            const isQuoteProduct = quickOrderItem.dataset.isQuote === 'true';
            let value = parseInt(input.value, 10) || 0;

            if (btn.classList.contains('decrease')) {
                if (value > 1) {
                    value--;
                } else if (value === 1) {
                    // Hiển thị modal xác nhận khi số lượng là 1 và ấn nút trừ
                    ModalCommon.showConfirm(
                        `Bạn có muốn xóa sản phẩm "${productName}" khỏi ${isQuoteProduct ? 'danh sách báo giá' : 'giỏ hàng'}?`,
                        function() {
                            // Xóa sản phẩm khỏi cart/quote
                            if (isQuoteProduct) {
                                removeFromQuote(productId);
                            } else {
                                removeFromCart(productId);
                            }
                        }
                    );
                    return;
                } else {
                    // Đã là 0, không thể giảm thêm
                    showToast({
                        type: 'warning',
                        message: `Sản phẩm "${productName}" đã ở số lượng tối thiểu (0)`
                    });
                    return;
                }
            } else if (btn.classList.contains('increase')) {
                const stock = parseInt(quickOrderItem.dataset.stock) || 0;

                // Kiểm tra stock cho sản phẩm có giá
                if (!isQuoteProduct && stock > 0 && value >= stock) {
                    showToast({
                        type: 'warning',
                        message: `Sản phẩm "${productName}" đã đạt số lượng tối đa trong kho (${stock})`
                    });
                    return;
                }

                value++;

                // ✅ LOGIC ĐỘC BIỆT: Khi cộng từ 0 lên 1 → ADD TO CART/QUOTE
                if (value === 1) {
                    if (isQuoteProduct) {
                        addToQuote(productId, 1);
                    } else {
                        addToCart(productId, 1);
                    }
                }
            }

            // Cập nhật UI ngay lập tức
            input.value = value;
            checkbox.checked = value > 0;
            updateItemTotal(quickOrderItem, value);
            updateSelectedItems();
            updateSelectAllCheckbox();

            // Gọi API cập nhật quantity (nếu không phải add từ 0 lên 1)
            if (!(btn.classList.contains('increase') && value === 1)) {
                debouncedUpdateQuickOrderQuantity(productId, value, isQuoteProduct);
            }

            // Lưu vào localStorage
            saveQuickOrderData();
        });
    });

    // ✅ DEBOUNCED UPDATE QUANTITY (COPY TỪ CART)
    const debouncedUpdateQuickOrderQuantity = debounce((productId, quantity, isQuoteProduct) => {
        const apiUrl = isQuoteProduct ? '/api/medical/quote/update-quantity-by-product' : '/api/medical/cart/update-quantity-by-product';

        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Cập nhật header count
                if (isQuoteProduct && data.quote_count !== undefined) {
                    updateHeaderQuoteCount(data.quote_count);
                } else if (!isQuoteProduct && data.cart_count !== undefined) {
                    updateHeaderCartCount(data.cart_count);
                }
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra khi cập nhật số lượng'
                });
            }
        })
        .catch(error => {
            console.error('Error updating quantity:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi cập nhật số lượng'
            });
        });
    }, 1000);

    // ✅ REMOVE FROM CART/QUOTE FUNCTIONS
    function removeFromCart(productId) {
        fetch('/api/medical/cart/remove-by-product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Cập nhật UI
                const quickOrderItem = document.querySelector(`.quick-order-item[data-id="${productId}"]`);
                if (quickOrderItem) {
                    const input = quickOrderItem.querySelector('.quantity-input');
                    const checkbox = quickOrderItem.querySelector('.item-checkbox');
                    input.value = 0;
                    checkbox.checked = false;
                    updateItemTotal(quickOrderItem, 0);
                    updateSelectedItems();
                    updateSelectAllCheckbox();
                    saveQuickOrderData();
                }

                // Cập nhật header count
                if (data.cart_count !== undefined) {
                    updateHeaderCartCount(data.cart_count);
                }

                showToast({
                    type: 'success',
                    message: data.message || 'Đã xóa sản phẩm khỏi giỏ hàng'
                });
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra'
                });
            }
        })
        .catch(error => {
            console.error('Error removing from cart:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi xóa sản phẩm'
            });
        });
    }

    function removeFromQuote(productId) {
        fetch('/api/medical/quote/remove-by-product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Cập nhật UI
                const quickOrderItem = document.querySelector(`.quick-order-item[data-id="${productId}"]`);
                if (quickOrderItem) {
                    const input = quickOrderItem.querySelector('.quantity-input');
                    const checkbox = quickOrderItem.querySelector('.item-checkbox');
                    input.value = 0;
                    checkbox.checked = false;
                    updateItemTotal(quickOrderItem, 0);
                    updateSelectedItems();
                    updateSelectAllCheckbox();
                    saveQuickOrderData();
                }

                // Cập nhật header count
                if (data.quote_count !== undefined) {
                    updateHeaderQuoteCount(data.quote_count);
                }

                showToast({
                    type: 'success',
                    message: data.message || 'Đã xóa sản phẩm khỏi danh sách báo giá'
                });
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra'
                });
            }
        })
        .catch(error => {
            console.error('Error removing from quote:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi xóa sản phẩm'
            });
        });
    }
    // Hàm thêm sản phẩm vào giỏ hàng
    function addToCart(productId, quantity) {
        if (!productId) {
            showToast({
                type: 'error',
                message: 'Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.'
            });
            return;
        }

        // Gọi API thêm vào giỏ hàng
        fetch('/api/medical/cart/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                showToast({
                    type: 'success',
                    message: data.message || 'Đã thêm sản phẩm vào giỏ hàng'
                });

                // Cập nhật số lượng sản phẩm trong giỏ hàng (nếu có)
                if (data.cart_count !== undefined) {
                    updateHeaderCartCount(data.cart_count);
                }
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi thêm vào giỏ hàng'
            });
        });
    }

    // Hàm thêm sản phẩm vào báo giá
    function addToQuote(productId, quantity) {
        if (!productId) {
            showToast({
                type: 'error',
                message: 'Không tìm thấy thông tin sản phẩm. Vui lòng thử lại sau.'
            });
            return;
        }

        // Gọi API thêm vào báo giá
        fetch('/api/medical/quote', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity,
                unit_type: 1 // Default unit type
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                showToast({
                    type: 'success',
                    message: data.message || 'Đã thêm sản phẩm vào danh sách báo giá'
                });

                // Cập nhật số lượng sản phẩm trong báo giá (nếu có)
                if (data.quote_count !== undefined) {
                    updateHeaderQuoteCount(data.quote_count);
                }
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi thêm vào danh sách báo giá'
            });
        });
    }

    // Hàm cập nhật số lượng báo giá ở header (nếu có)
    function updateHeaderQuoteCount(quoteCount = null) {
        // Cập nhật số lượng ở header
        const quoteCountElements = document.querySelectorAll('.quote-count, [data-quote-count]');
        quoteCountElements.forEach(element => {
            element.textContent = quoteCount;
            if (quoteCount > 0) {
                element.style.display = 'inline';
            } else {
                element.style.display = 'none';
            }
        });
    }



    // Hàm cập nhật số lượng hiển thị trong quick order
    function updateQuickOrderQuantityDisplay(productId, newQuantity) {
        const quickOrderItem = document.querySelector(`.quick-order-item[data-id="${productId}"]`);
        if (!quickOrderItem) {
            return;
        }

        const quantityInput = quickOrderItem.querySelector('.quantity-input');
        const checkbox = quickOrderItem.querySelector('.item-checkbox');

        if (quantityInput) {
            quantityInput.value = newQuantity;
        }

        if (checkbox) {
            checkbox.checked = newQuantity > 0;
        }

        // Cập nhật tổng tiền cho item này
        updateItemTotal(quickOrderItem, newQuantity);

        // Cập nhật tổng tiền chung
        updateSelectedItems();
        updateSelectAllCheckbox();

        // Lưu vào localStorage
        saveQuickOrderData();
    }

    // Helper function để format giá tiền
    function formatPrice(price) {
        if (typeof price !== 'number' || isNaN(price)) {
            return '0đ';
        }
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Load dữ liệu từ localStorage khi trang được tải
    function loadQuickOrderData() {
        try {
            const savedData = localStorage.getItem(QUICK_ORDER_STORAGE_KEY);

            if (savedData) {
                const data = JSON.parse(savedData);

                // Restore số lượng cho từng sản phẩm
                Object.keys(data.products || {}).forEach(productId => {
                    const productData = data.products[productId];
                    const quickOrderItem = document.querySelector(`.quick-order-item[data-id="${productId}"]`);

                    if (quickOrderItem && productData.quantity > 0) {
                        const quantityInput = quickOrderItem.querySelector('.quantity-input');
                        const checkbox = quickOrderItem.querySelector('.item-checkbox');

                        if (quantityInput) {
                            quantityInput.value = productData.quantity;
                        }
                        if (checkbox) {
                            checkbox.checked = true;
                        }

                        updateItemTotal(quickOrderItem, productData.quantity);
                    }
                });

                // Cập nhật tổng tiền
                updateSelectedItems();
                updateSelectAllCheckbox();

                // Cập nhật total count
                updateTotalSelectedCount();
            }
        } catch (error) {
            console.error('Error loading quick order data:', error);
            localStorage.removeItem(QUICK_ORDER_STORAGE_KEY);
        }
    }

    // Lưu dữ liệu vào localStorage
    function saveQuickOrderData() {
        try {
            const data = {
                products: {},
                timestamp: Date.now()
            };

            let savedCount = 0;

            // Lưu số lượng của từng sản phẩm
            document.querySelectorAll('.quick-order-item').forEach(item => {
                const productId = item.dataset.id;
                const quantityInput = item.querySelector('.quantity-input');
                const checkbox = item.querySelector('.item-checkbox');
                const price = parseFloat(item.dataset.price) || 0;
                const isQuote = item.dataset.isQuote === 'true';

                if (quantityInput && checkbox) {
                    const quantity = parseInt(quantityInput.value) || 0;

                    if (quantity > 0) {
                        data.products[productId] = {
                            quantity: quantity,
                            price: price,
                            checked: checkbox.checked,
                            isQuote: isQuote
                        };
                        savedCount++;
                    }
                }
            });

            localStorage.setItem(QUICK_ORDER_STORAGE_KEY, JSON.stringify(data));

            // Cập nhật total count
            updateTotalSelectedCount();
        } catch (error) {
            console.error('Error saving quick order data:', error);
        }
    }

    // Cập nhật hiển thị tổng số sản phẩm đã chọn
    function updateTotalSelectedCount() {
        try {
            const savedData = localStorage.getItem(QUICK_ORDER_STORAGE_KEY);

            const summaryCount = document.getElementById('summaryCount');
            const summaryTotal = document.getElementById('summaryTotal');


            if (savedData) {
                const data = JSON.parse(savedData);
                const products = data.products || {};
                const productCount = Object.keys(products).length;

                // Tính tổng số lượng và tổng tiền từ localStorage
                let totalQuantity = 0;
                let totalPrice = 0;

                Object.values(products).forEach(product => {
                    if (product && typeof product.quantity === 'number' && typeof product.price === 'number') {
                        totalQuantity += product.quantity;

                        // Chỉ cộng tiền nếu KHÔNG phải sản phẩm báo giá
                        if (!product.isQuote) {
                            totalPrice += product.quantity * product.price;
                        }
                    }
                });



                // Cập nhật summary sidebar
                if (summaryCount) {
                    summaryCount.textContent = totalQuantity;
                }
                if (summaryTotal) {
                    summaryTotal.textContent = formatPrice(totalPrice);
                    summaryTotal.style.color = '';
                    summaryTotal.style.fontWeight = '';
                }


            } else {
                // Không có data, reset về 0
                if (summaryCount) summaryCount.textContent = '0';
                if (summaryTotal) summaryTotal.textContent = '0đ';
            }
        } catch (error) {
            console.error('Error updating total selected count:', error);
        }
    }





    // Thêm event listeners cho checkboxes (chỉ thêm một lần)
    document.querySelectorAll('.item-checkbox:not([data-listener-added])').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            saveQuickOrderData();
        });
        checkbox.setAttribute('data-listener-added', 'true');
    });

    // Load data ngay lập tức
    function initQuickOrder() {
        if (isQuickOrderInitialized) {
            return;
        }

        isQuickOrderInitialized = true;
        // ✅ DISABLE LOCALSTORAGE LOAD - CHỈ DÙNG SERVER DATA
        // loadQuickOrderData();
        updateTotalSelectedCount();
    }

    // Load data khi trang được tải
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initQuickOrder);
    } else {
        // DOM đã sẵn sàng, load ngay
        setTimeout(initQuickOrder, 50);
    }

    // Xử lý tìm kiếm
    const searchForm = document.querySelector('.quick-order-search-form');
    const searchInput = document.querySelector('.quick-order-search-input');
    let searchTimeout;
    let isSearching = false;

    if (searchForm && searchInput) {
        // Xử lý submit form
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();

            // Gọi API để search với keyword bất kỳ
            loadQuickOrderSuggestions(searchTerm);
        });

        // Xử lý realtime search
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            // Gọi API với keyword (có thể rỗng)
            searchTimeout = setTimeout(() => {
                loadQuickOrderSuggestions(searchTerm);
            }, 300);
        });

        // Xử lý nút clear (X) nếu có
        searchInput.addEventListener('search', function() {
            // Event này trigger khi người dùng nhấn nút X trong search input (webkit browsers)
            if (this.value === '') {
                loadQuickOrderSuggestions('');
            }
        });

        // Xử lý phím ESC để clear search
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                loadQuickOrderSuggestions('');
            }
        });
    }

    // Function để reset về trạng thái ban đầu
    function resetToOriginalState() {
        const currentUrl = new URL(window.location);

        // Chỉ reset nếu đang có search parameter
        if (currentUrl.searchParams.has('search')) {
            currentUrl.searchParams.delete('search');
            currentUrl.searchParams.delete('page');

            // Hiển thị loading indicator
            showToast({
                type: 'info',
                message: 'Đang tải lại danh sách sản phẩm...'
            });

            // Redirect về trang gốc
            window.location.href = currentUrl.toString();
        }
    }

    // Function để load tất cả sản phẩm (không có search) qua AJAX
    function loadAllProducts() {
        const currentUrl = new URL(window.location);

        // Nếu có search parameter trong URL, redirect để xóa nó
        if (currentUrl.searchParams.has('search')) {
            currentUrl.searchParams.delete('search');
            currentUrl.searchParams.delete('page');
            window.location.href = currentUrl.toString();
            return;
        }

        // Gọi API để load tất cả sản phẩm (search với keyword rỗng)
        loadQuickOrderSuggestions('');
    }

    // Function để load gợi ý sản phẩm real-time
    function loadQuickOrderSuggestions(keyword) {
        if (isSearching) return;
        isSearching = true;

        // Hiển thị loading indicator
        const productTable = document.querySelector('.quick-order-table');
        if (productTable) {
            productTable.style.opacity = '0.6';
        }

        fetch(`/api/quick-order/search?q=${encodeURIComponent(keyword)}`)
            .then(response => response.json())
            .then(data => {
                if (data.products && data.products.length > 0) {
                    // Cập nhật danh sách sản phẩm với kết quả tìm kiếm
                    updateQuickOrderProducts(data.products, keyword);
                } else {
                    // Hiển thị thông báo không tìm thấy
                    showNoResultsMessage(keyword, data.message);
                }
            })
            .catch(error => {
                console.error('Error loading suggestions:', error);
                showToast({
                    type: 'error',
                    message: 'Có lỗi xảy ra khi tìm kiếm sản phẩm'
                });
            })
            .finally(() => {
                isSearching = false;
                if (productTable) {
                    productTable.style.opacity = '1';
                }
            });
    }

    // Function để cập nhật danh sách sản phẩm
    function updateQuickOrderProducts(products, keyword) {
        const productContainer = document.querySelector('.quick-order-table');
        if (!productContainer) return;

        // Tìm container chứa các sản phẩm (bỏ qua header)
        const existingItems = productContainer.querySelectorAll('.quick-order-item');

        // Xóa các sản phẩm hiện tại
        existingItems.forEach(item => item.remove());

        // Thêm sản phẩm mới
        products.forEach(product => {
            const productHtml = createQuickOrderItemHtml(product);
            productContainer.insertAdjacentHTML('beforeend', productHtml);
        });

        // Khởi tạo lại event listeners cho các sản phẩm mới
        initializeProductEventListeners();

        // ✅ SYNC QUANTITY CHO SẢN PHẨM MỚI SAU KHI SEARCH
        syncQuantityFromServer();

        // Cập nhật lại tổng tiền và UI
        updateSelectedItems();
    }

    // Function để tạo HTML cho sản phẩm
    function createQuickOrderItemHtml(product) {
        // Class cho item (dựa trên stock và visible_price)
        const itemStockClass = product.stock_status === 'in-stock' ? 'in-stock' : 'out-of-stock';
        const priceClass = product.visible_price ? '' : 'quote-product';

        // Class cho status badge
        const statusBadgeClass = product.stock_status === 'in-stock' ? 'status-in-stock' : 'status-out-of-stock';
        const stockIcon = product.stock_status === 'in-stock' ? 'fa-check-circle' : 'fa-times-circle';

        const isQuote = !product.visible_price;
        const increaseDisabled = (!product.visible_price || product.stock > 0) ? '' : 'disabled';

        return `
            <div class="quick-order-item ${itemStockClass} ${priceClass}" data-id="${product.id}" data-price="${product.price}" data-stock="${product.stock}" data-is-quote="${isQuote}">
                <div class="quick-order-checkbox-cell">
                    <input type="checkbox" id="quick-item-${product.id}" class="quick-order-checkbox item-checkbox">
                    <label for="quick-item-${product.id}" class="sr-only">Chọn sản phẩm</label>
                    <div class="quick-order-product-info">
                        <img src="${product.image}" alt="${product.name}" class="quick-order-product-image">
                        <div class="quick-order-product-details">
                            <h3 class="quick-order-product-name">${product.name}</h3>
                            <p class="quick-order-product-sku">${product.sku}</p>
                        </div>
                    </div>
                </div>
                <div class="quick-order-price-cell">
                    <span class="product-price">${product.price_text}</span>
                </div>
                <div class="quick-order-status-cell">
                    <span class="status-badge ${statusBadgeClass}">
                        <i class="fa-solid ${stockIcon}"></i>
                        ${product.stock_text}
                    </span>
                    <div class="stock-quantity">${product.stock_quantity}</div>
                </div>
                <div class="quick-order-quantity-cell">
                    <div class="quantity-control">
                        <button class="quantity-btn decrease">−</button>
                        <input type="text" class="quantity-input" value="0" readonly>
                        <button class="quantity-btn increase" ${increaseDisabled}>+</button>
                    </div>
                </div>
                <div class="quick-order-total-cell">
                    <span class="product-total">0đ</span>
                </div>
            </div>
        `;
    }

    // Function để hiển thị thông báo không tìm thấy
    function showNoResultsMessage(keyword, message) {
        const productContainer = document.querySelector('.quick-order-table');
        if (!productContainer) return;

        // Xóa các sản phẩm hiện tại
        const existingItems = productContainer.querySelectorAll('.quick-order-item');
        existingItems.forEach(item => item.remove());

        // Thêm thông báo không tìm thấy
        const noResultsHtml = `
            <div class="quick-order-no-results">
                <div style="text-align: center; padding: 40px 20px; color: #666;">
                    <i class="fa-solid fa-search" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <h3 style="margin-bottom: 8px;">Không tìm thấy sản phẩm</h3>
                    <p>Không có sản phẩm nào phù hợp với từ khóa "<strong>${keyword}</strong>"</p>
                    <button onclick="resetToOriginalState()" style="margin-top: 16px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Xem tất cả sản phẩm
                    </button>
                </div>
            </div>
        `;

        productContainer.insertAdjacentHTML('beforeend', noResultsHtml);
    }

    // Function để khởi tạo lại event listeners
    function initializeProductEventListeners() {
        // Khởi tạo lại quantity buttons
        document.querySelectorAll('.quantity-btn').forEach(btn => {
            // Xóa event listeners cũ nếu có
            btn.replaceWith(btn.cloneNode(true));
        });

        // Thêm lại event listeners cho quantity buttons
        document.querySelectorAll('.quantity-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                // Logic xử lý quantity (copy từ code hiện tại)
                handleQuantityChange(this);
            });
        });

        // Khởi tạo lại checkbox listeners
        document.querySelectorAll('.item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedItems);
        });
    }

    // Function để xử lý thay đổi quantity (dùng cho sản phẩm mới)
    function handleQuantityChange(btn) {
        const quickOrderItem = btn.closest('.quick-order-item');
        const input = quickOrderItem.querySelector('.quantity-input');
        const checkbox = quickOrderItem.querySelector('.item-checkbox');
        let value = parseInt(input.value, 10) || 0;

        if (btn.classList.contains('decrease')) {
            if (value > 0) {
                const productId = quickOrderItem.dataset.id;
                const productName = quickOrderItem.querySelector('.quick-order-product-name').textContent.trim();
                const oldValue = value;
                const wasChecked = checkbox.checked;

                value--;

                if (value === 0) {
                    checkbox.checked = false;
                }

                // Cập nhật UI ngay lập tức
                input.value = value;
                updateItemTotal(quickOrderItem, value);
                updateSelectedItems();
                updateSelectAllCheckbox();

                // Lưu vào localStorage
                saveQuickOrderData();

                // Hiển thị toast cho decrease
                showToast({
                    type: 'info',
                    message: `Đã giảm 1 sản phẩm "${productName}"`
                });
            }
        } else if (btn.classList.contains('increase')) {
            const productId = quickOrderItem.dataset.id;
            const productName = quickOrderItem.querySelector('.quick-order-product-name').textContent.trim();
            const stock = parseInt(quickOrderItem.dataset.stock) || 0;

            // Kiểm tra stock
            if (stock <= 0) {
                showToast({
                    type: 'error',
                    message: `Sản phẩm "${productName}" hiện đã hết hàng`
                });
                input.value = 0;
                checkbox.checked = false;
                updateItemTotal(quickOrderItem, 0);
                updateSelectedItems();
                return;
            }

            if (value >= stock) {
                showToast({
                    type: 'error',
                    message: `Sản phẩm "${productName}" chỉ còn ${stock} sản phẩm trong kho`
                });
                input.value = stock;
                value = stock;
                if (stock > 0) {
                    checkbox.checked = true;
                }
                updateItemTotal(quickOrderItem, stock);
                updateSelectedItems();
                return;
            }

            value++;
            checkbox.checked = true;

            // Cập nhật UI ngay lập tức
            input.value = value;
            updateItemTotal(quickOrderItem, value);
            updateSelectedItems();
            updateSelectAllCheckbox();

            // Lưu vào localStorage
            saveQuickOrderData();

            // Hiển thị toast cho increase
            showToast({
                type: 'success',
                message: `Đã thêm 1 sản phẩm "${productName}"`
            });
        }
    }

    // Function global để clear search (được gọi từ nút clear)
    window.clearSearch = function() {
        const searchInput = document.querySelector('.quick-order-search-input');
        if (searchInput) {
            searchInput.value = '';
        }
        loadQuickOrderSuggestions('');
    }


});
</script>

<style>
/* Cột trạng thái */
.quick-order-status-cell {
    width: 120px;
    padding: 12px 8px;
    text-align: center;
}

.quick-order-table-header .quick-order-status-cell {
    font-weight: 600;
    background-color: #f9fafb;
    color: #374151;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.status-in-stock {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-out-of-stock {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.stock-quantity {
    font-size: 11px;
    color: #6b7280;
    margin-top: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .quick-order-status-cell {
        width: 100px;
        padding: 8px 4px;
    }

    .status-badge {
        font-size: 11px;
        padding: 3px 6px;
    }

    .stock-quantity {
        font-size: 10px;
    }
}

/* Cập nhật grid layout để bao gồm cột trạng thái */
.quick-order-table-header,
.quick-order-item {
    display: grid;
    grid-template-columns: 2fr 1fr 120px 120px 1fr;
    gap: 0;
    align-items: center;
}

@media (max-width: 768px) {
    .quick-order-table-header,
    .quick-order-item {
        grid-template-columns: 2fr 1fr 100px 100px 1fr;
    }
}
</style>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/quick_order/quick_order.blade.php ENDPATH**/ ?>