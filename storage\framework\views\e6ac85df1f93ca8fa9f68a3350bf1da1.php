<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Thêm font Be Vietnam Pro giống với app layout -->
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap" rel="stylesheet">
    <?php echo app('App\Services\MedicalViteService')->renderAssets('resources/themes/medical/css/signin.css'); ?>
    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <title><PERSON><PERSON><PERSON> nhập - Medical Shop</title>
    
    <style>
        body, input, button, select {
            font-family: 'Be Vietnam Pro', sans-serif !important;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <a href="/home" class="logo">
            <img src="/images/full_logo_footer.png" alt="Velocity Logo">
        </a>

        <div class="login-form">
            <h1>Đăng nhập</h1>
            <form id="form-signin" action="<?php echo e(route('signin.submit')); ?>" method="POST" novalidate>
                <?php echo csrf_field(); ?>
                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" placeholder=""
                        value="<?php echo e(old('email')); ?>"
                        class="<?php echo e(session('error_email') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if(session('error_email')): ?>
                        <span class="error-message"><?php echo e(session('error_email')); ?></span>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="password">Mật khẩu <span class="required">*</span></label>
                    <input type="password" id="password" name="password" placeholder=""
                        class="<?php echo e(session('error_password') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if(session('error_password')): ?>
                        <span class="error-message"><?php echo e(session('error_password')); ?></span>
                    <?php endif; ?>
                </div>

                <div class="form-options">
                    <div class="show-password">
                        <input type="checkbox" id="show-password">
                        <label for="show-password" class="label-show-password">Hiện mật khẩu</label>
                    </div>
                    <a href="<?php echo e(route('medical.forgot_password.create')); ?>" class="forgot-password">Quên mật khẩu?</a>
                </div>

                <button type="submit" class="sign-in-btn">Đăng nhập</button>

                <div class="create-account">
                    <span>Bạn chưa có tài khoản?</span>
                    <a href="/signup"><strong>Tạo tài khoản mới</strong></a>
                </div>
            </form>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            const showPasswordCheckbox = document.getElementById('show-password');

            showPasswordCheckbox.addEventListener('change', function() {
                passwordField.type = this.checked ? 'text' : 'password';
            });

            // Xử lý validation form
            const form = document.getElementById('form-signin');

            // Ngăn chặn validation mặc định của trình duyệt
            if (form) {
                form.setAttribute('novalidate', true);

                // Xử lý sự kiện submit form
                form.addEventListener('submit', function(event) {
                    let isValid = true;

                    // Kiểm tra các trường bắt buộc
                    const requiredFields = form.querySelectorAll('[required]');
                    requiredFields.forEach(function(field) {
                        // Xóa thông báo lỗi cũ nếu có
                        const existingError = field.parentElement.querySelector('.error-message:not([data-server-error])');
                        if (existingError) {
                            existingError.remove();
                        }

                        // Kiểm tra trường có giá trị hay không
                        if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('is-invalid');

                            // Kiểm tra xem đã có thông báo lỗi từ server chưa
                            const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                            if (!serverError) {
                                // Tạo thông báo lỗi
                                const errorMessage = document.createElement('span');
                                errorMessage.className = 'error-message';
                                errorMessage.textContent = 'Vui lòng nhập thông tin này.';

                                // Thêm thông báo lỗi vào sau field
                                field.parentElement.appendChild(errorMessage);
                            }
                        } else {
                            // Nếu trường đã có giá trị, xóa class is-invalid
                            field.classList.remove('is-invalid');
                        }

                        // Kiểm tra định dạng email
                        if (field.type === 'email' && field.value.trim()) {
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test(field.value.trim())) {
                                isValid = false;
                                field.classList.add('is-invalid');

                                // Kiểm tra xem đã có thông báo lỗi từ server chưa
                                const serverError = field.parentElement.querySelector('.error-message[data-server-error]');
                                if (!serverError) {
                                    // Tạo thông báo lỗi
                                    const errorMessage = document.createElement('span');
                                    errorMessage.className = 'error-message';
                                    errorMessage.textContent = 'Email không đúng định dạng.';

                                    // Thêm thông báo lỗi vào sau field
                                    field.parentElement.appendChild(errorMessage);
                                }
                            }
                        }
                    });

                    // Nếu form không hợp lệ, ngăn chặn submit
                    if (!isValid) {
                        event.preventDefault();
                    }
                });

                // Đánh dấu các thông báo lỗi từ server
                const serverErrors = document.querySelectorAll('.error-message');
                serverErrors.forEach(function(error) {
                    error.setAttribute('data-server-error', 'true');
                });

                // Thêm sự kiện lắng nghe input để ẩn lỗi khi người dùng nhập
                const inputFields = form.querySelectorAll('input, select, textarea');
                inputFields.forEach(function(field) {
                    // Xử lý sự kiện input (khi người dùng nhập)
                    field.addEventListener('input', function() {
                        // Xóa thông báo lỗi
                        const errorMessage = field.parentElement.querySelector('.error-message');
                        if (errorMessage) {
                            errorMessage.remove();
                        }

                        // Xóa class is-invalid
                        field.classList.remove('is-invalid');
                    });
                });
            }
        });
    </script>

    <?php if(session('success')): ?>
        <script>
            toastr.success("<?php echo e(session('success')); ?>");
        </script>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <script>
            toastr.error("<?php echo e(session('error')); ?>");
        </script>
    <?php endif; ?>
</body>
</html>

<?php /**PATH /var/www/html/resources/themes/medical/views/signin/signin.blade.php ENDPATH**/ ?>