<?php

namespace Webkul\CartRule\Providers;

use Illuminate\Support\ServiceProvider;
use Webkul\CartRule\Console\Commands\ClearCartRuleCache;

class CartRuleOptimizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->mergeConfigFrom(
            base_path('config/cart-rule-optimization.php'), 'cart-rule-optimization'
        );
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                ClearCartRuleCache::class,
            ]);
        }

        // Publish config file
        $this->publishes([
            base_path('config/cart-rule-optimization.php') => config_path('cart-rule-optimization.php'),
        ], 'cart-rule-config');
    }
}
