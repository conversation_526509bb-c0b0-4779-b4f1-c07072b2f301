<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Webkul\Product\Models\Product;
use Webkul\Product\Models\ProductFlat;

class ProductController extends Controller
{
    /**
     * Xử lý dữ liệu sản phẩm trước khi truyền vào view
     */
    public function processProductData($products)
    {
        return $products->map(function ($product) {
            $productFlat = ProductFlat::where('product_id', $product->id)
                ->where('locale', app()->getLocale())
                ->where('channel', core()->getCurrentChannelCode())
                ->first();

            if ($productFlat) {
                $product->name = $productFlat->name;
                $product->price = $productFlat->price;
                $product->special_price = $productFlat->special_price;
                $product->visible_price = $productFlat->visible_price;
                $product->unit = $productFlat->unit;
            }

            // Lấy unit_type_id từ attribute_values
            $unitTypeAttr = $product->attribute_values->where('attribute.code', 'unit_type')->first();
            $product->unit_type_id = $unitTypeAttr ? $unitTypeAttr->integer_value : 1;

            // Lấy tên unit_type từ attribute options
            if ($unitTypeAttr && $unitTypeAttr->integer_value) {
                $unitTypeOption = DB::table('attribute_options')
                    ->where('id', $unitTypeAttr->integer_value)
                    ->first();
                $product->unit_type_name = $unitTypeOption ? $unitTypeOption->admin_name : 'sản phẩm';
            } else {
                $product->unit_type_name = 'sản phẩm';
            }

            // Xử lý giá cho sản phẩm configurable
            if ($product->type === 'configurable' && $product->variants->isNotEmpty()) {
                $firstVariant = $product->variants->first();
                $variantFlat = ProductFlat::where('product_id', $firstVariant->id)
                    ->where('locale', app()->getLocale())
                    ->where('channel', core()->getCurrentChannelCode())
                    ->first();

                if ($variantFlat) {
                    $product->price = $variantFlat->price;
                    $product->special_price = $variantFlat->special_price;
                    $product->visible_price = $variantFlat->visible_price;
                    $product->unit = $variantFlat->unit;
                }
            }

            // Xử lý discount
            if ($product->special_price && $product->price) {
                $product->has_discount = true;
                $product->original_price = $product->price;
                $product->price = $product->special_price;
                $product->discount = round((1 - ($product->special_price / $product->original_price)) * 100);
            }

            return $product;
        });
    }
}
