<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/illness_category.css']); ?>
<div class="illness-category-header">
    <div class="illness-category-header-left">
        <?php if(isset($category)): ?>
            <img src="<?php echo e($category->image_url ?? '/images/default-category.png'); ?>" alt="<?php echo e($category->name); ?>" class="illness-category-avatar">
            <div>
                <h1 class="illness-category-title"><?php echo e($category->name); ?></h1>
            </div>
        <?php elseif(isset($bodyPart)): ?>
            <img src="<?php echo e($bodyPart->image_url ?? '/images/default-body-part.png'); ?>" alt="<?php echo e($bodyPart->name); ?>" class="illness-category-avatar">
            <div>
                <h1 class="illness-category-title">Bệnh <?php echo e($bodyPart->name); ?></h1>
            </div>
        <?php else: ?>
            <img src="/images/default-category.png" alt="Tra cứu bệnh" class="illness-category-avatar">
            <div>
                <h1 class="illness-category-title">Tra cứu bệnh</h1>
            </div>
        <?php endif; ?>
    </div>
    <?php echo $__env->make('medical::common.search_bar', [
            'action' => '#',
            'method' => 'GET',
            'name' => 'keyword',
            'placeholder' => 'Tìm kiếm thông tin bệnh',
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<div class="illness-category-desc">
    <?php if(isset($category) && $category->description): ?>
        <?php echo e($category->description); ?>

    <?php elseif(isset($bodyPart) && $bodyPart->description): ?>
        <?php echo e($bodyPart->description); ?>

    <?php else: ?>
        Tìm hiểu thông tin chi tiết về các bệnh và cách phòng ngừa, điều trị hiệu quả.
    <?php endif; ?>
</div>
<hr class="illness-category-divider">


<div class="illness-tags-section">
    <div class="illness-tags-title">
        <?php if(isset($category)): ?>
            Bệnh thuộc <?php echo e($category->name); ?>

        <?php elseif(isset($bodyPart)): ?>
            Bệnh liên quan đến <?php echo e($bodyPart->name); ?>

        <?php else: ?>
            Danh sách bệnh
        <?php endif; ?>
    </div>
    <div class="illness-tags-diseases" id="illness-tags-diseases">
        <?php if(isset($illnesses) && $illnesses->count() > 0): ?>
            <?php
                $chunkedIllnesses = $illnesses->chunk(ceil($illnesses->count() / 3));
            ?>
            <?php $__currentLoopData = $chunkedIllnesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="illness-tags-col">
                <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $illness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('medical.illness_detail', $illness->slug)); ?>" class="illness-disease"><?php echo e($illness->name); ?></a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="illness-tags-col">
                <p class="text-gray-500">Chưa có bệnh nào trong danh mục này.</p>
            </div>
        <?php endif; ?>
    </div>
    <?php if(isset($illnesses) && $illnesses->count() > 10): ?>
    <div class="illness-tags-more-wrap">
        <button class="illness-tags-more-btn" id="illness-tags-more-btn">Xem thêm</button>
    </div>
    <?php endif; ?>
</div>

<?php if(isset($illnesses) && $illnesses->hasPages()): ?>
<div class="mt-6">
    <?php echo e($illnesses->links()); ?>

</div>
<?php endif; ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    const moreBtn = document.getElementById('illness-tags-more-btn');
    const diseasesList = document.getElementById('illness-tags-diseases');
    let expanded = false;
    if (moreBtn && diseasesList) {
        moreBtn.addEventListener('click', function() {
            expanded = !expanded;
            if (expanded) {
                diseasesList.style.maxHeight = '1000px';
                moreBtn.textContent = 'Thu gọn';
            } else {
                diseasesList.style.maxHeight = '320px';
                moreBtn.textContent = 'Xem thêm';
            }
        });
    }
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/illness_category/illness_category.blade.php ENDPATH**/ ?>