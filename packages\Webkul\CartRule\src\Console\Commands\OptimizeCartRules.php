<?php

namespace Webkul\CartRule\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class OptimizeCartRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'optimize:cart-rules {--test : Run performance test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize cart rules performance and fix timeout issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 Starting Cart Rule Performance Optimization...');
        $this->newLine();

        // Step 1: Clear cache
        $this->task('Clearing existing cache', function () {
            Cache::forget('cart_rule_condition_attributes');
            return true;
        });

        // Step 2: Optimize application
        $this->task('Optimizing application cache', function () {
            Artisan::call('config:cache');
            return true;
        });

        // Step 3: Analyze database
        $this->analyzeDatabase();

        // Step 4: Test configuration
        $this->testConfiguration();

        // Step 5: Performance test if requested
        if ($this->option('test')) {
            $this->runPerformanceTest();
        }

        // Step 6: Show recommendations
        $this->showRecommendations();

        $this->newLine();
        $this->info('✅ Cart Rule optimization completed!');
        
        return 0;
    }

    /**
     * Analyze database for optimization opportunities.
     */
    protected function analyzeDatabase()
    {
        $this->info('🔍 Analyzing database...');

        $stats = [];
        
        try {
            $stats['categories'] = DB::table('categories')->count();
            $stats['attributes'] = DB::table('attributes')->count();
            $stats['attribute_options'] = DB::table('attribute_options')->count();
            $stats['cart_rules'] = DB::table('cart_rules')->count();

            $this->table(
                ['Table', 'Count', 'Status'],
                [
                    ['Categories', $stats['categories'], $stats['categories'] > 1000 ? '⚠️  High' : '✅ OK'],
                    ['Attributes', $stats['attributes'], $stats['attributes'] > 100 ? '⚠️  High' : '✅ OK'],
                    ['Attribute Options', $stats['attribute_options'], $stats['attribute_options'] > 10000 ? '⚠️  High' : '✅ OK'],
                    ['Cart Rules', $stats['cart_rules'], '✅ OK'],
                ]
            );

            // Recommendations based on data size
            if ($stats['categories'] > 1000) {
                $this->warn('💡 Consider adding database index: categories(parent_id, position)');
            }
            
            if ($stats['attribute_options'] > 10000) {
                $this->warn('💡 Consider adding database index: attribute_options(attribute_id, sort_order)');
            }

        } catch (\Exception $e) {
            $this->error('❌ Database analysis failed: ' . $e->getMessage());
        }
    }

    /**
     * Test current configuration.
     */
    protected function testConfiguration()
    {
        $this->info('🧪 Testing configuration...');

        $config = config('cart-rule-optimization');
        
        if (!$config) {
            $this->error('❌ Configuration file not found. Run: php artisan vendor:publish --tag=cart-rule-config');
            return;
        }

        $this->table(
            ['Setting', 'Value', 'Status'],
            [
                ['Cache Enabled', $config['cache']['enabled'] ? 'Yes' : 'No', $config['cache']['enabled'] ? '✅' : '⚠️'],
                ['Cache TTL', $config['cache']['ttl'] . 's', '✅'],
                ['Lazy Loading', $config['lazy_loading']['enabled'] ? 'Yes' : 'No', $config['lazy_loading']['enabled'] ? '✅' : '⚠️'],
                ['Max Execution Time', $config['timeout']['max_execution_time'] . 's', '✅'],
                ['Memory Limit', $config['timeout']['memory_limit'], '✅'],
            ]
        );
    }

    /**
     * Run performance test.
     */
    protected function runPerformanceTest()
    {
        $this->info('⏱️  Running performance test...');

        try {
            $startTime = microtime(true);
            $startMemory = memory_get_usage();

            // Test loading condition attributes
            $cartRuleRepo = app('Webkul\CartRule\Repositories\CartRuleRepository');
            $attributes = $cartRuleRepo->getConditionAttributes();

            $endTime = microtime(true);
            $endMemory = memory_get_usage();

            $executionTime = round(($endTime - $startTime) * 1000, 2);
            $memoryUsed = round(($endMemory - $startMemory) / 1024 / 1024, 2);

            $this->table(
                ['Metric', 'Value', 'Status'],
                [
                    ['Execution Time', $executionTime . 'ms', $executionTime < 5000 ? '✅ Good' : '⚠️  Slow'],
                    ['Memory Used', $memoryUsed . 'MB', $memoryUsed < 50 ? '✅ Good' : '⚠️  High'],
                    ['Attributes Loaded', count($attributes), '✅'],
                ]
            );

            if ($executionTime > 5000) {
                $this->warn('💡 Consider enabling more aggressive caching or lazy loading');
            }

        } catch (\Exception $e) {
            $this->error('❌ Performance test failed: ' . $e->getMessage());
        }
    }

    /**
     * Show final recommendations.
     */
    protected function showRecommendations()
    {
        $this->newLine();
        $this->info('📋 Next Steps:');
        
        $envVars = [
            'CART_RULE_CACHE_ENABLED=true',
            'CART_RULE_LAZY_LOADING_ENABLED=true', 
            'CART_RULE_MAX_EXECUTION_TIME=300',
            'CART_RULE_MEMORY_LIMIT=512M'
        ];

        $this->line('1. Add these environment variables to your .env file:');
        foreach ($envVars as $var) {
            $this->line("   $var");
        }

        $this->newLine();
        $this->line('2. Register the service provider in config/app.php:');
        $this->line('   Webkul\\CartRule\\Providers\\CartRuleOptimizationServiceProvider::class,');

        $this->newLine();
        $this->line('3. Test the cart rule creation page:');
        $this->line('   ' . url('/admin/marketing/promotions/cart-rules/create'));

        $this->newLine();
        $this->line('4. Monitor performance and adjust settings as needed.');
        
        $this->newLine();
        $this->info('📖 For detailed documentation, see: CART_RULE_OPTIMIZATION_README.md');
    }
}
