<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/index.css']); ?>
<style>
/* Banner swipe/drag styles */
#banner {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: grab;
    position: relative;
}

#banner:active {
    cursor: grabbing;
}

#banner img {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    transition: opacity 0.3s ease-in-out;
    will-change: opacity;
}

#banner.dragging img {
    transition: none;
}

/* Improve dot visibility and interaction */
.dot {
    transition: opacity 0.3s ease, transform 0.2s ease;
}

.dot:hover {
    transform: scale(1.2);
    opacity: 1 !important;
}

/* Add visual feedback for touch devices */
@media (hover: none) and (pointer: coarse) {
    #banner {
        cursor: default;
    }

    #banner:active {
        cursor: default;
    }
}

/* Prevent text selection during drag */
#banner * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for banner content */
#banner .banner-content * {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Carousel styles */
.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.carousel-track {
    display: flex;
    transition: transform 0.5s ease-in-out;
    height: 100%;
}

.carousel-slide {
    min-width: 100%;
    position: relative;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-content {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10%;
    text-align: left;
    max-width: 800px;
}

.carousel-dots {
    z-index: 10;
}

.dot.active {
    opacity: 1 !important;
}

.carousel-arrow {
    z-index: 10;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.carousel-arrow:hover {
    opacity: 1;
}

@media (max-width: 414px) {
    .banner-content .text-\[4rem\] {
        font-size: 25px !important;
        width: 20rem !important;
    }
}

/* Responsive styles */
@media (max-width: 768px) {
    .banner-content .text-[4rem] leading-none {
        font-size: 25px !important;
        width: 20rem !important;
    }
    
    .banner-content .text-lg {
        font-size: 1rem;
    }
}
</style>
<!-- Banner -->
<div id="banner" class="relative overflow-hidden"> 
    <div class="relative h-[500px] w-full">
        <div class="carousel-container">
            <div class="carousel-track">
                <?php if($bannerData && count($bannerData) > 0): ?>
                    <?php $__currentLoopData = $bannerData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if(!empty($banner['link'])): ?>
                            <div class="carousel-slide clickable-slide" data-link="<?php echo e($banner['link']); ?>">
                                <img src="<?php echo e($banner['image']); ?>" alt="<?php echo e($banner['title'] ?: 'Slide ' . ($index + 1)); ?>" class="w-full h-full object-cover">
                            </div>
                        <?php else: ?>
                            <div class="carousel-slide">
                                <img src="<?php echo e($banner['image']); ?>" alt="<?php echo e($banner['title'] ?: 'Slide ' . ($index + 1)); ?>" class="w-full h-full object-cover">
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="carousel-slide">
                        <img src="/images/slider10.png" alt="Default Slide" class="w-full h-full object-cover">
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="carousel-dots absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <?php if($bannerData && count($bannerData) > 0): ?>
                <?php $__currentLoopData = $bannerData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button class="dot w-3 h-3 rounded-full bg-white opacity-60 transition-opacity duration-300" data-index="<?php echo e($index); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <button class="dot w-3 h-3 rounded-full bg-white opacity-60 transition-opacity duration-300 active" data-index="0"></button>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- About us -->
<div id="area-about-us">
    <div style="width: 97%; padding-left: 5%;">
        <img id="img-about-us" />
        <div id="about-us" class="shadow-sm">
            <h2 id="text-about-us">Chúng tôi là ai</h2>
            <p class="about-us-title" style="margin-top: 18px; font-size: 16px;">Công Ty Cổ Phần Dược Vật Tư Y Tế Phan Anh là đơn vị phân phối & sản xuất dược phẩm – thiết bị y tế uy tín tại Việt Nam. Với hơn 10 năm hoạt động, chúng tôi cung cấp hàng ngàn sản phẩm chất lượng, kiểm định rõ ràng, giá bán cạnh tranh cho hệ thống nhà thuốc, phòng khám, và các đơn vị y tế trên toàn quốc</p>
            <div onclick="window.location.href='/page/about-us'" id="more-button" class="more-button">
                Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
            </div>
        </div>
    </div>
</div>

<!--Introduce-->
<div id="introduce">
    <div id="introduce-title">
        <div id="introduce-title-text">5 Trụ cột tạo nên sự khác biệt</div>
    </div>
    <div class="introduce-content">
        <div class="introduce-row">
            <div class="introduce-item">
                <div class="introduce-item-header">
                    <div class="introduce-icon-bg">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F06F22" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12" />
                        </svg>
                    </div>
                    <div class="introduce-title">Sản phẩm đạt chuẩn chất lượng</div>
                </div>
                <div class="introduce-content-box">
                    <div class="introduce-text">Dược phẩm, thực phẩm chức năng và thiết bị y tế được kiểm định nghiêm ngặt, có nguồn gốc rõ ràng, hạn dùng minh bạch, phù hợp mọi nhu cầu sử dụng trong điều trị và chăm sóc sức khỏe.</div>
                </div>
            </div>
            <div class="introduce-item">
                <div class="introduce-item-header">
                    <div class="introduce-icon-bg">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F06F22" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12" />
                        </svg>
                    </div>
                    <div class="introduce-title">Dịch vụ tận tâm – chuyên biệt cho B2B</div>
                </div>
                <div class="introduce-content-box">
                    <div class="introduce-text">
                        <p>Hỗ trợ toàn diện cho đối tác:</p>
                        <ul>
                            <li>Giao hàng nhanh – đúng cam kết</li>
                            <li>Tư vấn chọn sản phẩm phù hợp</li>
                            <li>Dịch vụ hậu mãi – CSKH sau đơn hàng</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="introduce-item">
                <div class="introduce-item-header">
                    <div class="introduce-icon-bg">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F06F22" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12" />
                        </svg>
                    </div>
                    <div class="introduce-title">Đội ngũ chuyên nghiệp</div>
                </div>
                <div class="introduce-content-box">
                    <div class="introduce-text">Chúng tôi sở hữu đội ngũ chăm sóc khách hàng giàu kinh nghiệm, luôn sẵn sàng tư vấn 1:1 miễn phí, tận tình và chu đáo. Dược Phan Anh cam kết đem đến trải nghiệm mua hàng tốt nhất cho từng khách hàng, dù là đơn vị lớn hay nhà thuốc mới bắt đầu kinh doanh.</div>
                </div>
            </div>
        </div>
        <div class="introduce-row-2">
            <div class="introduce-item-2">
                <div class="introduce-item-header">
                    <div class="introduce-icon-bg">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F06F22" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12" />
                        </svg>
                    </div>
                    <div class="introduce-title">Kho tri thức sức khỏe</div>
                </div>
                <div class="introduce-content-box">
                    <div class="introduce-text">
                        <p>"Sổ tay nhà thuốc" – cẩm nang chuyên môn bao gồm:</p>
                        <ul>
                            <li>Thông tin sản phẩm mới</li>
                            <li>Hướng dẫn tư vấn khách hàng</li>
                        </ul>
                        <p>Kiến thức chuyên ngành cập nhật liên tục</p>
                    </div>
                </div>
            </div>
            <div class="introduce-item-2">
                <div class="introduce-item-header">
                    <div class="introduce-icon-bg">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#F06F22" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="20 6 9 17 4 12" />
                        </svg>
                    </div>
                    <div class="introduce-title">Vận chuyển toàn quốc – bảo quản chuẩn GSP</div>
                </div>
                <div class="introduce-content-box">
                    <div class="introduce-text">Quy trình bảo quản và giao hàng tuân thủ chuẩn GSP, đảm bảo chất lượng thuốc suốt quá trình vận chuyển, kể cả các sản phẩm nhạy cảm với nhiệt độ.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Impressive-Number-->
<div class="impressive-wrapper">
    <div id="impressive-img">
        <img src="/images/impressive-number.png" />
    </div>
    <div class="impressive-container">
        <div class="impressive-title">
            Những con số ấn tượng
        </div>
        <div class="impressive-numbers">
            <div>
                <div class="impressive-number">8.000+</div>
                <div class="impressive-label">Mặt hàng</div>
            </div>
            <div>
                <div class="impressive-number">500+</div>
                <div class="impressive-label">Đối tác đại lý</div>
            </div>
            <div>
                <div class="impressive-number">100+</div>
                <div class="impressive-label">Nhãn hàng</div>
            </div>
        </div>

        <!-- Phần liên hệ -->
        <div class="contact-section">
            <div class="container-fluid px-3">
                <div class="contact-title">
                    Liên hệ với chúng tôi
                </div>
                <div class="row g-3 justify-content-center">
                    <!-- Hotline -->
                    <div class="col-12 col-md-12 col-lg-4 contact-box">
                        <div class="contact-item d-flex align-items-center">
                            <i class="fa-solid fa-phone-volume contact-icon me-3"></i>
                            <div class="flex-grow-1">
                                <div class="contact-label text-start">Hotline</div>
                                <div class="contact-value">240 3856 328</div>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="col-12 col-md-12 col-lg-4 contact-box">
                        <div class="contact-item d-flex align-items-center">
                            <i class="fa-solid fa-envelope contact-icon me-3"></i>
                            <div class="flex-grow-1">
                                <div class="contact-label text-start">Email</div>
                                <div class="contact-value"><EMAIL></div>
                            </div>
                        </div>
                    </div>

                    <!-- Zalo -->
                    <div class="col-12 col-md-12 col-lg-4 contact-box">
                        <div class="contact-item d-flex align-items-center">
                            <div class="contact-zalo-wrapper me-3">
                                <img src="/images/zalo.png" alt="Zalo" class="contact-zalo-icon" />
                            </div>
                            <div class="flex-grow-1">
                                <div class="contact-label text-start">Zalo</div>
                                <div class="contact-value">zalo.com/nhathuocso</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- products -->
<?php if($companyProgramProducts && $companyProgramProducts->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Danh mục theo chương trình của công ty</h2>
        <a href="<?php echo e(route('our_product', ['company_program' => '1'])); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px] max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__currentLoopData = $companyProgramProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('medical::common.product_card', [
                'product' => $product
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php endif; ?>

<!-- products -->
<?php if($productsBestSeller && $productsBestSeller->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Danh mục sản phẩm bán chạy</h2>
        <a href="<?php echo e(route('our_product', ['sort' => 'best_seller'])); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__currentLoopData = $productsBestSeller; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('medical::common.product_card', [
                'product' => $product
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php endif; ?>

<!-- products -->
<?php if($productsNew && $productsNew->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Danh mục sản phẩm mới</h2>
        <a href="<?php echo e(route('our_product', ['sort' => 'newest'])); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__currentLoopData = $productsNew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('medical::common.product_card', [
                'product' => $product
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php endif; ?>

<!-- products -->
<?php if($productsFeatured && $productsFeatured->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Danh mục sản phẩm nổi bật</h2>
        <a href="<?php echo e(route('our_product', ['featured' => '1'])); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__currentLoopData = $productsFeatured; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $__env->make('medical::common.product_card', [
                'product' => $product
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php endif; ?>

<!-- products -->
<?php if($exclusiveProducts && $exclusiveProducts->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Danh mục độc quyền</h2>
        <a href="<?php echo e(route('our_product', ['exclusive' => '1'])); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__currentLoopData = $exclusiveProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="bg-white rounded-2xl border border-gray-300 p-3 product-card">
            <a href="<?php echo e(route('product_detail', ['productId' => $product->id])); ?>" class="product-link flex flex-col h-full">
                <div class="relative product-image-container">
                    <img src="<?php echo e($product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png')); ?>" alt="Product" class="w-full h-48 object-cover rounded-lg">
                    
                    <?php if(($product->discount > 0 || $product->discount_percent) && $product->visible_price && Auth::guard('customer')->check()): ?>
                    <span class="discount absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-sm rounded">-<?php echo e($product->discount_percent ??  $product->discount); ?>%</span>
                    <?php endif; ?>
                </div>
                <div class="mt-3 product-content">
                    <h3 class="font-semibold text-gray-800 mb-2 product-title"><?php echo e($product->name); ?></h3>
                    <div class="product-price-section">
                        <?php if($product->price && $product->visible_price): ?>
                            <?php if(Auth::guard('customer')->check()): ?>
                                <?php if($product->has_discount || $product->discount_percent): ?>
                                    <p class="text-gray-500 line-through text-sm"><?php echo e(number_format($product->original_price)); ?>đ</p>
                                <?php endif; ?>
                                <p class="text-[#F06F22] font-semibold"><?php echo e(number_format($product->discounted_price ?? $product->price)); ?>đ/ <?php echo e($product->unit_type_name ?? 'sản phẩm'); ?></p>
                            <?php else: ?>
                                <div class="h-[40px]"></div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="h-[40px]"></div>
                        <?php endif; ?>
                    </div>
                </div>
            </a>
            <button class="mt-3 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors w-full quick-order-add-btn product-button" data-product-id="<?php echo e($product->id); ?>">
                <?php if(Auth::guard('customer')->check()): ?>
                    <?php echo e($product->price && $product->visible_price ? 'Thêm vào giỏ hàng' : 'Báo giá'); ?>

                <?php else: ?>
                    <?php echo e($product->price && $product->visible_price ? 'Đăng nhập để xem giá' : 'Đăng nhập để báo giá'); ?>

                <?php endif; ?>
            </button>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php endif; ?>

<!-- popular-brand -->
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Thương hiệu nổi bật</h2>
        <button onclick="window.location.href='/brands'" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-[#FFEDE2] rounded-2xl p-4 flex flex-col items-center cursor-pointer hover:shadow-lg transition-shadow duration-300"
                 onclick="window.location.href='/brands_detail/<?php echo e($brand->id); ?>'">
                <!-- Logo thương hiệu -->
                <div class="" style="padding: 5px;background: #ffff;border-radius: 8px;">
                    <?php if($brand->brand_image): ?>
                        <img src="<?php echo e(asset('storage/' . $brand->brand_image)); ?>"
                             alt="<?php echo e($brand->brand_name); ?>"
                             class="h-16 object-contain">
                    <?php else: ?>
                        <img src="<?php echo e(asset('images/default-brand.png')); ?>"
                             alt="<?php echo e($brand->brand_name); ?>"
                             class="h-16 object-contain">
                    <?php endif; ?>
                </div>

                <!-- Sản phẩm nổi bật của thương hiệu -->
                <?php if($brand->outstanding_product): ?>
                    <div class="bg-white rounded-3xl p-[20px] aspect-square w-full my-[20px]">
                        <div class="relative w-full h-full">
                            <?php if($brand->outstanding_product->images->first() && $brand->outstanding_product->images->first()->path): ?>
                                <img src="<?php echo e(asset('storage/' . $brand->outstanding_product->images->first()->path)); ?>"
                                     alt="<?php echo e($brand->outstanding_product->name); ?>"
                                     class="w-full h-full object-contain rounded-lg">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500">Không có hình ảnh</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if($brand->outstanding_product->special_price): ?>
                        <div class="flex items-center gap-2">
                            <span class="text-black-500 font-bold">Giảm đến</span>
                            <span class="text-orange-500 font-bold text-xl" style="margin-bottom: 1%;">
                                <?php echo e(round((1 - $brand->outstanding_product->special_price / $brand->outstanding_product->price) * 100)); ?>%
                            </span>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!--illness-research-->
<?php if(isset($illnessCategories) && $illnessCategories->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Tra cứu bệnh</h2>
        <a href="<?php echo e(route('medical.illness')); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__currentLoopData = $illnessCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="illness-card">
            <div class="flex items-start gap-[20px]">
                <div class="h-[225px] aspect-[1/2] max-xl:h-[200px] rounded-2xl overflow-hidden bg-[lightgray]">
                    <img src="<?php echo e($category->image_url ?? asset('images/default-illness.png')); ?>"
                        alt="<?php echo e($category->name); ?>"
                        onclick="window.location.href='<?php echo e(route('medical.illness_category.show', $category->slug)); ?>'"
                        style="cursor: pointer;"
                        class="w-full h-full object-cover">
                </div>
                <div class="illness-card-content">
                    <h3 onclick="window.location.href='<?php echo e(route('medical.illness_category.show', $category->slug)); ?>'"
                        style="cursor: pointer;"><?php echo e($category->name); ?></h3>
                    <div class="ill-list">
                        <?php $__currentLoopData = $category->illnesses->take(7); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $illness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="illness" onclick="window.location.href='<?php echo e(route('medical.illness_detail', $illness->slug)); ?>'"><?php echo e($illness->name); ?></span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($category->illnesses->count() > 7): ?>
                            <span class="illness" style="color: #F06F22;"><a href="<?php echo e(route('medical.illness_category.show', $category->slug)); ?>" class="text-orange-500 hover:text-orange-600 font-bold">Xem thêm <i class="fa-solid fa-arrow-right"></i></a></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php else: ?>
<!-- Fallback: No illness categories found -->
<div class="container mx-auto px-4 py-8 max-lg:!px-0">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Tra cứu bệnh</h2>
        <a href="<?php echo e(route('medical.illness')); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">
            Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i>
        </a>
    </div>
    <div class="text-center py-8">
        <p class="text-gray-600">Chưa có dữ liệu danh mục bệnh. Vui lòng thêm dữ liệu trong admin panel.</p>
    </div>
</div>
<?php endif; ?>

<!-- News Section -->
<?php if($latestNews->count() > 0): ?>
<div class="container mx-auto px-4 py-8 max-lg:!px-0" id="news-section">
    <div class="flex justify-between items-center mb-6 gap-2">
        <h2 class="text-[32px] font-bold max-md::text-lg max-xl:text-2xl">Tin tức</h2>
        <a href="<?php echo e(route('news')); ?>" class="more-button bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors max-lg:!px-2 max-lg:!py-1 text-[14px]">Xem thêm <i class="fa-solid fa-arrow-right" style="color: #ff6b00;"></i></a>
    </div>

    <!-- News Categories -->
    <div class="news-categories">
        <div class="category-tag active" onclick="window.location.href='<?php echo e(route('news')); ?>'">
            Tất cả
        </div>
        <?php $__currentLoopData = $newsCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="category-tag" onclick="window.location.href='<?php echo e(route('news', ['category' => $category->slug])); ?>'">
                <?php echo e($category->name); ?>

            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <?php if($featuredNews): ?>
        <!-- Layout có tin nổi bật: 1 tin to + 2 cột tin nhỏ -->
        <div class="news-grid">
            <!-- Main News (Featured) -->
            <div class="main-news border border-gray-300 rounded-lg" onclick="console.log('Clicking featured news:', '<?php echo e($featuredNews->slug); ?>'); window.location.href='<?php echo e(route('news.detail', $featuredNews->slug)); ?>'">
                <img src="<?php echo e($featuredNews->featured_image_url); ?>" alt="<?php echo e($featuredNews->title); ?>">
                <div class="news-content">
                    <span class="news-tag"><?php echo e($featuredNews->category ? $featuredNews->category->name : 'Tin tức'); ?></span>
                    <h3 class="text-xl font-semibold mt-2"><?php echo e($featuredNews->title); ?></h3>
                    <p class="mt-2"><?php echo e($featuredNews->excerpt); ?></p>
                </div>
            </div>

            <!-- Column 2 -->
            <div class="flex flex-col gap-6 h-[85%]">
                <?php
                    // Loại trừ tin nổi bật khỏi danh sách tin bên
                    $otherNews = $latestNews->filter(function($news) use ($featuredNews) {
                        return $news->id !== $featuredNews->id;
                    });
                    $newsChunk1 = $otherNews->slice(0, 3);
                ?>
                <?php $__empty_1 = true; $__currentLoopData = $newsChunk1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="small-news border border-gray-300 rounded-lg" onclick="console.log('Clicking news:', '<?php echo e($newsItem->slug); ?>'); window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'" style="cursor: pointer; position: relative; z-index: 1;">
                        <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>">
                        <div class="news-content">
                            <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                            <h3 style="font-size: 14px;"><?php echo e(Str::limit($newsItem->title, 50)); ?></h3>
                            <p style="font-size: 14px;"><?php echo e(Str::limit($newsItem->excerpt, 80)); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <!-- No news in this column -->
                <?php endif; ?>
            </div>

            <!-- Column 3 -->
            <div class="flex flex-col gap-6 h-[85%]">
                <?php $newsChunk2 = $otherNews->slice(3, 3) ?>
                <?php $__empty_1 = true; $__currentLoopData = $newsChunk2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="small-news border border-gray-300 rounded-lg" onclick="console.log('Clicking news:', '<?php echo e($newsItem->slug); ?>'); window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'" style="cursor: pointer; position: relative; z-index: 1;">
                        <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>">
                        <div class="news-content">
                            <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                            <h3 style="font-size: 14px;"><?php echo e(Str::limit($newsItem->title, 50)); ?></h3>
                            <p style="font-size: 14px;"><?php echo e(Str::limit($newsItem->excerpt, 80)); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <!-- No news in this column -->
                <?php endif; ?>
            </div>
        </div>
    <?php elseif($latestNews->count() > 0): ?>
        <!-- Layout không có tin nổi bật: tất cả tin cùng size -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $latestNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="small-news border border-gray-300 rounded-lg" onclick="console.log('Clicking news:', '<?php echo e($newsItem->slug); ?>'); window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'" style="cursor: pointer; position: relative; z-index: 1;">
                    <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>">
                    <div class="news-content">
                        <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                        <h3><?php echo e(Str::limit($newsItem->title, 50)); ?></h3>
                        <p><?php echo e(Str::limit($newsItem->excerpt, 80)); ?></p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php else: ?>
        <!-- Placeholder if no news at all -->
        <div class="text-center py-8">
            <p class="text-gray-500">Hiện tại chưa có tin tức nào được đăng tải.</p>
        </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<div id="toast-data"
     data-success="<?php echo e(session('success')); ?>"
     data-error="<?php echo e(session('error')); ?>">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const track = document.querySelector('.carousel-track');
    const slides = document.querySelectorAll('.carousel-slide');
    const dots = document.querySelectorAll('.dot');
    const banner = document.getElementById('banner');
    
    let currentIndex = 0;
    let isTransitioning = false;
    let autoSlideInterval;
    let startX, moveX;
    let isDragging = false;

    function updateCarousel(index) {
        if (isTransitioning) return;
        isTransitioning = true;

        // Update dots
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });

        // Update slide position
        track.style.transform = `translateX(-${index * 100}%)`;

        currentIndex = index;

        setTimeout(() => {
            isTransitioning = false;
        }, 500);
    }

    function nextSlide() {
        const nextIndex = (currentIndex + 1) % slides.length;
        updateCarousel(nextIndex);
    }

    function prevSlide() {
        const prevIndex = (currentIndex - 1 + slides.length) % slides.length;
        updateCarousel(prevIndex);
    }

    function startAutoSlide() {
        // Clear any existing interval first
        clearInterval(autoSlideInterval);
        autoSlideInterval = setInterval(nextSlide, 3000);
    }

    function stopAutoSlide() {
        clearInterval(autoSlideInterval);
        autoSlideInterval = null;
    }

    // Touch/Drag event handlers
    banner.addEventListener('mousedown', (e) => {
        if (e.target.closest('.dot, button, a, input, textarea, select, [role="button"]')) return;
        isDragging = true;
        startX = e.clientX;
        banner.classList.add('dragging');
        stopAutoSlide();
    });

    banner.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        
        moveX = e.clientX;
        const diff = moveX - startX;
        const offset = -currentIndex * 100 + (diff / banner.offsetWidth) * 100;
        
        // Limit the drag range
        if (offset > 0 || offset < -(slides.length - 1) * 100) return;
        
        track.style.transform = `translateX(${offset}%)`;
    });

    banner.addEventListener('mouseup', (e) => {
        if (!isDragging) return;
        
        isDragging = false;
        banner.classList.remove('dragging');
        
        moveX = e.clientX;
        const diff = moveX - startX;
        
        // Determine if we should change slides
        if (Math.abs(diff) > banner.offsetWidth * 0.2) {
            if (diff > 0 && currentIndex > 0) {
                prevSlide();
            } else if (diff < 0 && currentIndex < slides.length - 1) {
                nextSlide();
            } else {
                updateCarousel(currentIndex);
            }
        } else {
            updateCarousel(currentIndex);
        }
        
        startAutoSlide();
    });

    banner.addEventListener('mouseleave', () => {
        if (isDragging) {
            isDragging = false;
            banner.classList.remove('dragging');
            updateCarousel(currentIndex);
            startAutoSlide();
        }
    });

    // Touch events for mobile
    banner.addEventListener('touchstart', (e) => {
        if (e.target.closest('.dot, button, a, input, textarea, select, [role="button"]')) return;
        isDragging = true;
        startX = e.touches[0].clientX;
        banner.classList.add('dragging');
        stopAutoSlide();
    });

    banner.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        
        moveX = e.touches[0].clientX;
        const diff = moveX - startX;
        const offset = -currentIndex * 100 + (diff / banner.offsetWidth) * 100;
        
        // Limit the drag range
        if (offset > 0 || offset < -(slides.length - 1) * 100) return;
        
        track.style.transform = `translateX(${offset}%)`;
    });

    banner.addEventListener('touchend', (e) => {
        if (!isDragging) return;
        
        isDragging = false;
        banner.classList.remove('dragging');
        
        moveX = e.changedTouches[0].clientX;
        const diff = moveX - startX;
        
        // Determine if we should change slides
        if (Math.abs(diff) > banner.offsetWidth * 0.2) {
            if (diff > 0 && currentIndex > 0) {
                prevSlide();
            } else if (diff < 0 && currentIndex < slides.length - 1) {
                nextSlide();
            } else {
                updateCarousel(currentIndex);
            }
        } else {
            updateCarousel(currentIndex);
        }
        
        startAutoSlide();
    });

    // Event listeners for buttons and dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            if (index !== currentIndex) {
                updateCarousel(index);
                stopAutoSlide();
                // Restart auto slide after a small delay to avoid double trigger
                setTimeout(() => {
                    startAutoSlide();
                }, 100);
            }
        });
    });

    // Start auto sliding
    startAutoSlide();

    // Pause auto sliding on hover
    banner.addEventListener('mouseenter', () => {
        stopAutoSlide();
    });
    banner.addEventListener('mouseleave', () => {
        // Only restart if not currently dragging
        if (!isDragging) {
            startAutoSlide();
        }
    });

    // Handle click events for clickable slides (only if not dragging)
    document.querySelectorAll('.clickable-slide').forEach(slide => {
        let clickStartTime = 0;
        let clickStartX = 0;
        let clickStartY = 0;

        slide.addEventListener('mousedown', (e) => {
            clickStartTime = Date.now();
            clickStartX = e.clientX;
            clickStartY = e.clientY;
        });

        slide.addEventListener('click', (e) => {
            const clickEndTime = Date.now();
            const clickEndX = e.clientX;
            const clickEndY = e.clientY;

            // Calculate distance moved
            const distance = Math.sqrt(
                Math.pow(clickEndX - clickStartX, 2) +
                Math.pow(clickEndY - clickStartY, 2)
            );

            // Only navigate if it's a real click (not drag)
            // Time < 300ms and distance < 10px
            if (clickEndTime - clickStartTime < 300 && distance < 10) {
                const link = slide.getAttribute('data-link');
                if (link) {
                    window.location.href = link;
                }
            }
        });
    });

    // Toast notifications
    const toastData = document.getElementById('toast-data');
    if (toastData) {
        const success = toastData.dataset.success;
        const error = toastData.dataset.error;
        if (success) {
            Toastify({
                text: success,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#059669",
                close: true
            }).showToast();
        }
        if (error) {
            Toastify({
                text: error,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
        }
    }
});
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/home/<USER>/ ?>