<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Thêm font Be Vietnam Pro giống với app layout -->
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700&display=swap" rel="stylesheet">
    <?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/signin.css']); ?>
    <!-- jQuery (required for toastr) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <title>Quên mật khẩu - Medical Shop</title>

    <style>
        body, input, button {
            font-family: 'Be Vietnam Pro', sans-serif !important;
        }
        
        @media (max-width: 1024px) {
            body {
                padding-top: 10rem;
            }
        }

        @media (max-width: 768px) {
            input[type="email"] {
                width: 93%;
            }

            .login-form {
                width: 85%;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <a href="/home" class="logo">
            <img src="/images/full_logo_footer.png" alt="Medical Logo">
        </a>

        <div class="login-form">
            <h1>Quên mật khẩu</h1>
            <p style="color: #666; margin-bottom: 20px; font-size: 14px;">
                Nhập địa chỉ email của bạn và chúng tôi sẽ gửi cho bạn liên kết để đặt lại mật khẩu.
            </p>

            <form id="form-forgot-password" action="<?php echo e(route('medical.forgot_password.store')); ?>" method="POST" novalidate>
                <?php echo csrf_field(); ?>
                <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input type="email" id="email" name="email" placeholder="Nhập email của bạn"
                        value="<?php echo e(old('email')); ?>"
                        class="<?php echo e(session('error_email') ? 'is-invalid' : ''); ?>"
                        required>
                    <?php if(session('error_email')): ?>
                        <span class="error-message"><?php echo e(session('error_email')); ?></span>
                    <?php endif; ?>
                </div>

                <button type="submit" class="sign-in-btn">Gửi liên kết đặt lại</button>

                <div class="create-account">
                    <a href="/signin"><strong>Quay lại đăng nhập</strong></a>
                </div>
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Configure toastr
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "timeOut": "5000",
                "positionClass": "toast-top-right",
                "preventDuplicates": true
            };

            // Show success message
            <?php if(session('success')): ?>
                toastr.success('<?php echo e(session('success')); ?>');
            <?php endif; ?>

            // Show warning message
            <?php if(session('warning')): ?>
                toastr.warning('<?php echo e(session('warning')); ?>');
            <?php endif; ?>

            // Show error message
            <?php if(session('error')): ?>
                toastr.error('<?php echo e(session('error')); ?>');
            <?php endif; ?>

            // Form validation
            $('#form-forgot-password').on('submit', function(e) {
                const form = this;
                const emailField = document.getElementById('email');
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('.error-message').forEach(function(element) {
                    if (!element.textContent.includes('<?php echo e(session('error_email')); ?>')) {
                        element.remove();
                    }
                });
                document.querySelectorAll('.is-invalid').forEach(function(element) {
                    if (!element.classList.contains('<?php echo e(session('error_email') ? 'is-invalid' : ''); ?>')) {
                        element.classList.remove('is-invalid');
                    }
                });

                // Validate email
                if (!emailField.value.trim()) {
                    showFieldError(emailField, 'Vui lòng nhập email.');
                    isValid = false;
                } else if (!isValidEmail(emailField.value)) {
                    showFieldError(emailField, 'Email không đúng định dạng.');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            function showFieldError(field, message) {
                field.classList.add('is-invalid');
                const errorElement = document.createElement('span');
                errorElement.className = 'error-message';
                errorElement.textContent = message;
                field.parentNode.appendChild(errorElement);
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });
    </script>
</body>

</html>
<?php /**PATH /var/www/html/resources/themes/medical/views/forgot-password/forgot-password.blade.php ENDPATH**/ ?>