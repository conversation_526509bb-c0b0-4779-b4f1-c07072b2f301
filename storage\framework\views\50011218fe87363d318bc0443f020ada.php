<!-- Confirm Modal -->
<div id="confirm-modal" class="modal" style="display:none">
    <div class="modal-content">
        <p id="confirm-message">B<PERSON>n có chắc chắn muốn xóa sản phẩm này?</p>
        <div class="modal-actions">
            <button id="confirm-cancel" class="modal-btn cancel-btn">Hủy</button>
            <button id="confirm-delete" class="modal-btn delete-confirm-btn">Xóa</button>
        </div>
    </div>
</div>

<!-- Error <PERSON> -->
<div id="error-modal" class="modal" style="display:none">
    <div class="modal-content">
        <p id="error-message">Có lỗi xảy ra</p>
        <div class="modal-actions">
            <button id="error-ok" class="modal-btn cancel-btn">OK</button>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.3);
    justify-content: center;
    align-items: center;
}
.modal[style*="block"], .modal[style*="flex"] {
    display: flex !important;
}
.modal .modal-content {
    background: #fff;
    border-radius: 12px;
    padding: 32px 24px;
    width: 325px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    text-align: center;
    position: relative;
}
.modal-actions {
    margin-top: 24px;
    display: flex;
    justify-content: center;
    gap: 16px;
}
.modal-btn {
    padding: 10px 24px;
    border-radius: 6px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    background: #F06F22;
    color: #fff;
    transition: background 0.2s;
}
.modal-btn.cancel-btn {
    background: #F66B00;
    color: white;
}
.modal-btn.cancel-btn:hover {
    background: #d95c0b;
}
.modal-btn.delete-confirm-btn {
    background: #F06F22;
    color: #fff;
}
.modal-btn.delete-confirm-btn:hover {
    background: #d95c0b;
}
</style>

<script>
    window.ModalCommon = {
    showError: function(message) {
        const errorModal = document.getElementById('error-modal');
        const errorMessage = document.getElementById('error-message');
        if (errorMessage) errorMessage.textContent = message || 'Có lỗi xảy ra';
        if (errorModal) errorModal.style.display = 'flex';
    },
    hideError: function() {
        const errorModal = document.getElementById('error-modal');
        if (errorModal) errorModal.style.display = 'none';
    },
    showConfirm: function(message, onConfirm) {
        const confirmModal = document.getElementById('confirm-modal');
        const confirmMessage = document.getElementById('confirm-message');
        if (confirmMessage) confirmMessage.textContent = message || 'Bạn có chắc chắn?';
        if (confirmModal) confirmModal.style.display = 'flex';
        // Gắn sự kiện xác nhận
        const confirmBtn = document.getElementById('confirm-delete');
        const cancelBtn = document.getElementById('confirm-cancel');
        function cleanup() {
            confirmModal.style.display = 'none';
            confirmBtn.removeEventListener('click', onConfirmClick);
            cancelBtn.removeEventListener('click', onCancelClick);
        }
        function onConfirmClick() {
            cleanup();
            if (typeof onConfirm === 'function') onConfirm();
        }
        function onCancelClick() {
            cleanup();
        }
        confirmBtn.addEventListener('click', onConfirmClick);
        cancelBtn.addEventListener('click', onCancelClick);
        // Đóng khi click ra ngoài
        confirmModal.addEventListener('mousedown', function(e) {
            if (e.target === confirmModal) cleanup();
        }, { once: true });
    }
};

// Đóng modal lỗi khi bấm OK hoặc click ra ngoài
document.addEventListener('DOMContentLoaded', function() {
    const errorModal = document.getElementById('error-modal');
    const errorOk = document.getElementById('error-ok');
    if (errorOk) errorOk.addEventListener('click', ModalCommon.hideError);
    if (errorModal) {
        errorModal.addEventListener('mousedown', function(e) {
            if (e.target === errorModal) ModalCommon.hideError();
        });
    }
});
</script><?php /**PATH /var/www/html/resources/themes/medical/views/common/modal.blade.php ENDPATH**/ ?>