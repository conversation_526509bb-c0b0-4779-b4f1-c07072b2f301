
<div class="bg-white rounded-lg border border-gray-300 p-6 h-fit">
    <form id="product-filter-form" method="GET" action="<?php echo e($filterAction ?? request()->url()); ?>">
        
        <?php $__currentLoopData = request()->query(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(!in_array($key, ['sort', 'price_range', 'brands'])): ?>
                <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        
        <div class="flex justify-between items-center mb-6">
            <a href="<?php echo e($filterAction ?? request()->url()); ?><?php echo e(request()->has('category') ? '?category=' . request('category') : ''); ?><?php echo e(request()->has('search') ? (request()->has('category') ? '&' : '?') . 'search=' . request('search') : ''); ?>"
               class="text-orange-500 hover:text-orange-600 text-sm filter-reset-text">
                Thiết lập lại
            </a>
            <button type="submit" class="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors filter-apply-text">
                Áp dụng
            </button>
        </div>

        
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Sắp xếp:</label>
            <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500" onchange="this.form.submit()">
                <?php $__currentLoopData = $sortOptions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        // Disable price-related sort options when not logged in
                        $isPriceSort = in_array($value, ['price_asc', 'price_desc']);
                        $isDisabled = $isPriceSort && !Auth::guard('customer')->check();
                    ?>
                    <option value="<?php echo e($value); ?>"
                            <?php echo e(request('sort') == $value ? 'selected' : ''); ?>

                            <?php echo e($isDisabled ? 'disabled' : ''); ?>

                            style="<?php echo e($isDisabled ? 'color: #ccc;' : ''); ?>">
                        <?php echo e($label); ?><?php echo e($isDisabled ? ' (Cần đăng nhập)' : ''); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        
        <div class="mb-6 <?php echo e(!Auth::guard('customer')->check() ? 'opacity-50 pointer-events-none' : ''); ?>">
            <label class="block text-sm font-medium text-gray-700 mb-3">
                Khoảng giá
                <?php if(!Auth::guard('customer')->check()): ?>
                    <span class="text-xs text-gray-500">(Cần đăng nhập)</span>
                <?php endif; ?>
            </label>

            <div class="space-y-2">
                <?php $__currentLoopData = $priceRanges ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <label class="flex items-center">
                        <input type="radio" name="price_range" value="<?php echo e($value); ?>"
                               <?php echo e(request('price_range') == $value ? 'checked' : ''); ?>

                               <?php echo e(!Auth::guard('customer')->check() ? 'disabled' : ''); ?>

                               class="text-orange-500 focus:ring-orange-500 focus:ring-2">
                        <span class="ml-2 text-sm text-gray-700"><?php echo e($label); ?></span>
                    </label>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        
        <?php if(isset($brands) && $brands->count() > 0): ?>
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">Thương hiệu</label>

            
            <div class="mb-3">
                <input type="text" id="brand-search" placeholder="Nhập tên thương hiệu"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
            </div>

            
            <div class="space-y-2 max-h-48 overflow-y-auto" id="brand-list">
                <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <label class="flex items-center brand-item" data-brand-name="<?php echo e(strtolower($brand->brand_name)); ?>">
                        <input type="checkbox" name="brands[]" value="<?php echo e($brand->id); ?>"
                               <?php echo e(in_array($brand->id, request('brands', [])) ? 'checked' : ''); ?>

                               class="text-orange-500 focus:ring-orange-500 focus:ring-2">
                        <span class="ml-2 text-sm text-gray-700"><?php echo e($brand->brand_name); ?></span>
                    </label>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>
    </form>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Brand search functionality
    const brandSearch = document.getElementById('brand-search');
    const brandItems = document.querySelectorAll('.brand-item');

    if (brandSearch) {
        brandSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            brandItems.forEach(item => {
                const brandName = item.getAttribute('data-brand-name');
                if (brandName.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Handle form submit - reset page to 1
    const productFilterForm = document.getElementById('product-filter-form');
    if (productFilterForm) {
        productFilterForm.addEventListener('submit', function(e) {
            // Tìm input page hiện tại
            let pageInput = this.querySelector('input[name="page"]');

            if (pageInput) {
                // Nếu đã có input page, set về 1
                pageInput.value = '1';
            } else {
                // Nếu chưa có, tạo input hidden mới
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'page';
                hiddenInput.value = '1';
                this.appendChild(hiddenInput);
            }
        });
    }

    // Handle sort select change - reset page to 1
    const sortSelect = document.querySelector('select[name="sort"]');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const form = this.closest('form');
            if (form) {
                // Tìm input page hiện tại
                let pageInput = form.querySelector('input[name="page"]');

                if (pageInput) {
                    pageInput.value = '1';
                } else {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'page';
                    hiddenInput.value = '1';
                    form.appendChild(hiddenInput);
                }

                // Submit form
                form.submit();
            }
        });
    }
});
</script>

<style>
/* Responsive cho tablet (769px - 1023px) - Text "Thiết lập lại" và "Áp dụng" */
@media (min-width: 769px) and (max-width: 1023px) {
    .filter-reset-text {
        font-size: 12px !important;
    }

    .filter-apply-text {
        font-size: 12px !important;
    }
}
</style>
<?php /**PATH /var/www/html/resources/themes/medical/views/common/product-filter.blade.php ENDPATH**/ ?>