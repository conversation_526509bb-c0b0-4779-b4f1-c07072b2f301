<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/checkout.css']); ?>
<div class="checkout-container">
    <!-- Cột trái: Thông tin thanh toán -->
    <div class="checkout-left">
        <div class="checkout-section">
            <h2 class="section-title">Thanh toán</h2>
            <!-- Danh sách sản phẩm -->
            <?php if($selectedItems && $selectedItems->count() > 0): ?>
            <?php $__currentLoopData = $selectedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="product-summary">
                <img src="<?php echo e($item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                    alt="<?php echo e($item->name); ?>"
                    class="product-img">
                <div class="product-info">
                    <div class="product-name"><?php echo e($item->name); ?></div>
                    <?php if($item->additional && isset($item->additional['variant'])): ?>
                    <div class="product-variant"><?php echo e($item->additional['variant']); ?></div>
                    <?php endif; ?>
                </div>
                <div class="product-qty">x<?php echo e($item->quantity); ?></div>
                <div class="product-price">
                    <?php echo e(number_format($item->price * $item->quantity, 0, ',', '.')); ?> đ
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
            <p>Không có sản phẩm nào được chọn</p>
            <?php endif; ?>

            <div class="checkout-note">
                <label for="note">Ghi chú</label>
                <textarea id="note" name="note" placeholder="Nhập ghi chú ở đây"></textarea>
            </div>
            <div class="form-group" style="margin-top: 10px;">
                <label>Phương thức vận chuyển</label><br>
                <label><input type="radio" name="shipping_method" value="free_shipping" checked required> Miễn phí vận chuyển</label><br>
                
            </div>
            <div class="form-group" style="margin-top: 10px;">
                <label>Phương thức thanh toán</label><br>
                <label><input type="radio" name="payment_method" value="qr_payment" checked required> Thanh toán qua QR</label><br>
            </div>
        </div>

        <div class="checkout-section">
            <div class="address-info-row">
                <div>
                    <?php
                    $customer = auth()->guard('customer')->user();
                    $address = $customer ? $customer->addresses->where('default_address', 1)->first() : null;
                    ?>
                    <div class="address-title">Thông tin người nhận</div>
                    <div class="address-user">
                        <?php if($address && $address->company_name): ?>
                        <span class="address-company"><?php echo e($address->company_name); ?></span> |
                        <?php endif; ?>
                        <?php if($address && $address->email): ?>
                        <span class="address-email"><?php echo e($address->email); ?></span> |
                        <?php endif; ?>
                        <span class="address-name"><?php echo e($address ? $address->first_name : ''); ?> <?php echo e($address ? $address->last_name : ''); ?></span>
                        <span class="address-phone">| <?php echo e($address ? $address->phone : ''); ?></span>
                    </div>
                    <div class="address-detail">
                        <?php if($customer): ?>
                        <?php if($address): ?>
                        <?php echo e(is_array($address->address) ? implode(', ', $address->address) : $address->address); ?>

                        <?php else: ?>
                        Chưa có địa chỉ
                        <?php endif; ?>
                        <?php else: ?>
                        Vui lòng đăng nhập để tiếp tục
                        <?php endif; ?>
                    </div>
                </div>
                <a href="#" class="address-change">Thay đổi</a>
            </div>
        </div>
    </div>

    <!-- Cột phải: Chi tiết thanh toán -->
    <div class="checkout-right">
        <div class="checkout-summary">
            <div class="summary-detail">
                <div class="summary-row">
                    <span>Tạm tính</span>
                    <span>
                        <?php echo e(number_format($total, 0, ',', '.')); ?> đ
                    </span>
                </div>
                <div class="summary-row">
                    <span>Phí vận chuyển</span>
                    <span id="shipping-fee">0 đ</span>
                </div>
            </div>
            <div class="summary-total">
                <div>
                    <span class="total-label">Tổng tiền</span>
                    <span class="total-note"><?php echo e($selectedItems->count()); ?> sản phẩm</span>
                </div>
                <div class="total-value">
                    <?php echo e(number_format($total, 0, ',', '.')); ?> đ
                </div>
            </div>
            <div class="summary-terms">
                <input type="checkbox" id="terms" required>
                <label for="terms">Bằng cách tích vào ô chọn, bạn đã đồng ý với <a href="/page/dieu-khoan">Điều khoản Dược Phan Anh</a></label>
            </div>
            <form id="checkout-form" action="<?php echo e(route('checkout.placeOrder')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="selected_items" value="<?php echo e(json_encode($selectedItems->pluck('id'))); ?>">
                <input type="hidden" name="payment_method" value="cashondelivery">
                <input type="hidden" name="shipping_method" id="shipping_method_input" value="free_shipping">
                <input type="hidden" name="selected_address_id" id="selected_address_id" value="<?php echo e($address ? $address->id : ''); ?>">
                <input type="hidden" name="note" id="note_input" value="">
                <button type="submit" class="order-btn">
                    Đặt hàng
                </button>
            </form>
        </div>
    </div>

    <!-- address modal -->
    <div id="addressModal" class="modal-address" style="display:none;">
        <div class="modal-address-content">
            <div class="modal-address-header">
                <span class="modal-address-title">Địa chỉ giao hàng</span>
                <span class="modal-address-close" id="closeAddressModal">&times;</span>
            </div>
            <div class="modal-address-list">
                <?php $__currentLoopData = auth()->guard('customer')->user()->addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <label class="modal-address-item">
                
                    <input type="radio" name="addressRadio" value="<?php echo e($address->id); ?>" data-default="<?php echo e($address->default_address); ?>" <?php echo e($address->default_address ? 'checked' : ''); ?>>
                    <span>
                        <b><?php echo e($address->first_name); ?> <?php echo e($address->last_name); ?></b> | <?php echo e($address->phone); ?><br>
                        <?php echo e($address->address); ?><br>
                    </span>
                    <a href="#" class="modal-address-update">Cập nhật</a>
                </label>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="modal-address-actions">
                <button class="modal-address-add"><i class="fa fa-plus"></i> Thêm địa chỉ</button>
                <div style="flex:1"></div>
                <button class="modal-address-back">Quay lại</button>
                <button class="modal-address-apply" id="applyAddressBtn">Áp dụng</button>
            </div>
        </div>
    </div>
</div>

<!-- update address modal -->
<div id="editAddressModal" class="modal-address" style="display:none;">
    <div class="modal-address-content" style="max-width: 420px;">
        <div class="modal-address-header">
            <span class="modal-address-title" id="editAddressTitle">Cập nhật địa chỉ</span>
            <span class="modal-address-close" id="closeEditAddressModal">&times;</span>
        </div>
        <form id="editAddressForm" autocomplete="off" action="<?php echo e(route('checkout.create-new-address')); ?>" method="POST">
            <div style="padding: 0 20px;">
                <input id="add_id" type="hidden" name="add_id" value="">
                <div class="form-group">
                    
                    <label>Họ và tên</label>
                    <input type="text" id="editName" name="name" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>Số điện thoại</label>
                    <input type="text" id="editPhone" name="phone" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>Địa chỉ</label>
                    <textarea id="editAddress" class="form-control" name="address" rows="2" required></textarea>
                </div>
                <div class="form-group" style="display: none">
                    <label>Loại địa chỉ</label>
                    <div style="display: flex; gap: 8px; margin-bottom: 6px;">
                        <button type="button" class="type-btn" data-type="Nhà riêng" id="typeHome">Nhà riêng</button>
                        <button type="button" class="type-btn" data-type="Công ty" id="typeCompany">Công ty</button>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="editDefault" name="default">
                        Đặt làm địa chỉ mặc định
                    </label>
                </div>
            </div>
            <div class="modal-address-actions">
                <button type="button" class="modal-address-back" id="editAddressBack">Quay lại</button>
                <button type="button" class="modal-address-apply" id="saveEditAddressBtn" onclick="saveEditAddress()">Lưu lại</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        let originalSelectedAddressId = null; // Lưu ID địa chỉ ban đầu
        document.querySelector('.address-change').addEventListener('click', function(e) {
            e.preventDefault();

            // Lưu ID địa chỉ hiện tại đang được chọn
            const currentSelected = document.querySelector('input[name="addressRadio"]:checked');
            originalSelectedAddressId = currentSelected ? currentSelected.value : null;

            document.getElementById('addressModal').style.display = 'flex';
        });

        // Đóng modal khi ấn nút X - khôi phục lựa chọn ban đầu
        document.getElementById('closeAddressModal').onclick = function() {
            restoreOriginalSelection();
            document.getElementById('addressModal').style.display = 'none';
        };

        // Đóng modal khi ấn "Quay lại" - khôi phục lựa chọn ban đầu
        document.querySelector('.modal-address-back').onclick = function() {
            restoreOriginalSelection();
            document.getElementById('addressModal').style.display = 'none';
        };

        // Hàm khôi phục lựa chọn ban đầu
        function restoreOriginalSelection() {
            if (originalSelectedAddressId) {
                // Bỏ chọn tất cả radio buttons
                document.querySelectorAll('input[name="addressRadio"]').forEach(radio => {
                    radio.checked = false;
                });
                // Chọn lại radio button ban đầu
                const originalRadio = document.querySelector(`input[name="addressRadio"][value="${originalSelectedAddressId}"]`);
                if (originalRadio) {
                    originalRadio.checked = true;
                }
            }
        }

        // Áp dụng địa chỉ đã chọn
        document.getElementById('applyAddressBtn').onclick = function() {
            const checked = document.querySelector('input[name="addressRadio"]:checked');
            if (!checked) {
                alert('Vui lòng chọn một địa chỉ');
                return;
            }
            const label = checked.closest('.modal-address-item');
            const info = label.querySelector('span').innerHTML.split('<br>');
            const [namePhone, address] = info;
            const [name, phone] = namePhone.replace('<b>', '').replace('</b>', '').split('|').map(s => s.trim());

            // Cập nhật thông tin hiển thị
            document.querySelector('.address-name').textContent = name;
            document.querySelector('.address-phone').textContent = '| ' + phone;
            document.querySelector('.address-detail').textContent = address;
            // Cập nhật ID địa chỉ vào form checkout
            document.getElementById('selected_address_id').value = checked.value;
            // Cập nhật originalSelectedAddressId thành địa chỉ mới được chọn
            originalSelectedAddressId = checked.value;

            document.getElementById('addressModal').style.display = 'none';
        };

        // THÊM kiểm tra vào submit form:
        document.getElementById('checkout-form').addEventListener('submit', function(e) {
            if (!document.getElementById('terms').checked) {
                alert('Vui lòng đồng ý với điều khoản trước khi đặt hàng');
                e.preventDefault();
                return false;
            }
            
            // Lấy giá trị note từ textarea và gán vào hidden input
            const noteValue = document.getElementById('note').value.trim();
            document.getElementById('note_input').value = noteValue;
        });

        // Lấy các phần tử cần thiết
        const shippingRadios = document.querySelectorAll('input[name="shipping_method"]');
        const totalValue = document.querySelector('.total-value');
        const shippingFeeEl = document.getElementById('shipping-fee');
        const baseTotal = Number('<?php echo e($total); ?>');
        const shippingMethodInput = document.getElementById('shipping_method_input');

        function updateTotal() {
            let shippingFee = 0;
            const checked = document.querySelector('input[name="shipping_method"]:checked');
            if (checked && checked.value === 'flat_rate') {
                shippingFee = 30000;
            }

                //subtotalEl.textContent = baseTotal.toLocaleString('vi-VN') + ' đ';
                const newTotal = baseTotal + shippingFee;
                totalValue.textContent = newTotal.toLocaleString('vi-VN') + ' đ';
                document.querySelector('.order-btn').textContent = 'Đặt hàng';

            if (shippingFeeEl) {
                shippingFeeEl.textContent = shippingFee > 0 ? shippingFee.toLocaleString('vi-VN') + ' đ' : '0 đ';
            }
            shippingMethodInput.value = checked ? checked.value : 'free_shipping';
        }

        shippingRadios.forEach(radio => {
            radio.addEventListener('change', updateTotal);
        });

        updateTotal();
    });

    // --- MODAL CẬP NHẬT/THÊM ĐỊA CHỈ ---
    const editModal = document.getElementById('editAddressModal');
    const editForm = document.getElementById('editAddressForm');
    const editTitle = document.getElementById('editAddressTitle');
    const editName = document.getElementById('editName');
    const editPhone = document.getElementById('editPhone');
    const editAddress = document.getElementById('editAddress');
    const typeHome = document.getElementById('typeHome');
    const typeCompany = document.getElementById('typeCompany');
    const editDefault = document.getElementById('editDefault');
    let editingLabel = null; // Địa chỉ đang sửa, null nếu là thêm mới

    function setType(type) {
        if (type === 'Nhà riêng') {
            typeHome.classList.add('active');
            typeCompany.classList.remove('active');
        } else {
            typeHome.classList.remove('active');
            typeCompany.classList.add('active');
        }
        typeHome.dataset.selected = (type === 'Nhà riêng');
        typeCompany.dataset.selected = (type === 'Công ty');
    }

    function openEditAddressModal(mode, label = null) {
        editingLabel = label;
        const radio = label ? label.querySelector('input[name="addressRadio"]') : null;
        document.getElementById('add_id').value = radio ? radio.value : '';
        
        // Kiểm tra số lượng địa chỉ
        const addressCount = document.querySelectorAll('.modal-address-item').length;
        
        if (mode === 'edit' && label) {
            editTitle.textContent = 'Cập nhật địa chỉ';
            const info = label.querySelector('span').innerHTML.split('<br>');
            const [namePhone, addressText] = info;
            const [name, phone] = namePhone.replace('<b>', '').replace('</b>', '').split('|').map(s => s.trim());
            editName.value = name;
            editPhone.value = phone;
            editAddress.value = addressText ? addressText.trim() : '';
            setType('Nhà riêng');
            editDefault.checked = radio.dataset.default == "1";
            
            // Logic xử lý checkbox mặc định
            if (radio.dataset.default == "1") {
                // Địa chỉ này đang là mặc định: không cho phép bỏ mặc định
                editDefault.disabled = true;
                editDefault.checked = true;
            } else {
                // Địa chỉ này không phải mặc định: cho phép thay đổi
                editDefault.disabled = false;
            }
        } else {
            editTitle.textContent = 'Địa chỉ mới';
            editName.value = '';
            editPhone.value = '';
            editAddress.value = '';
            setType('Nhà riêng');
            
            // Nếu không có địa chỉ nào, địa chỉ đầu tiên sẽ là mặc định
            if (addressCount === 0) {
                editDefault.checked = true;
                editDefault.disabled = true;
            } else {
                editDefault.checked = false;
                editDefault.disabled = false;
            }
        }
        editModal.style.display = 'flex';
    }

    function closeEditAddressModal() {
        editModal.style.display = 'none';
    }

    // Gán lại sự kiện cho tất cả nút "Cập nhật" (kể cả nút mới thêm)
    function bindUpdateButtons() {
        document.querySelectorAll('.modal-address-update').forEach(function(btn) {
            btn.onclick = function(e) {
                e.preventDefault();
                const label = btn.closest('.modal-address-item');
                openEditAddressModal('edit', label);
            };
        });
    }
    bindUpdateButtons();

    // Nút "Thêm địa chỉ"
    document.querySelector('.modal-address-add').addEventListener('click', function(e) {
        e.preventDefault();
        openEditAddressModal('add');
    });

    // Đóng modal cập nhật/thêm địa chỉ
    document.getElementById('closeEditAddressModal').onclick = closeEditAddressModal;
    document.getElementById('editAddressBack').onclick = function(e) {
        e.preventDefault();
        closeEditAddressModal();
    };

    // Chọn loại địa chỉ
    typeHome.onclick = function(e) {
        e.preventDefault();
        setType('Nhà riêng');
    }
    typeCompany.onclick = function(e) {
        e.preventDefault();
        setType('Công ty');
    }

    // Xử lý submit form cập nhật/thêm địa chỉ
    editForm.onsubmit = function(e) {
        e.preventDefault();
        const add_id = document.getElementById('add_id').getAttribute('data-id');
        const name = editName.value.trim();
        const phone = editPhone.value.trim();
        const address = editAddress.value.trim();
        const type = typeHome.classList.contains('active') ? 'Nhà riêng' : 'Công ty';
        if (editingLabel) {
            editingLabel.querySelector('span').innerHTML =
                `<b>${name}</b> | ${phone}<br>${address}<br>
                `;
        } else {
            const newLabel = document.createElement('label');
            newLabel.className = 'modal-address-item';
            newLabel.innerHTML = `
            <input type="radio" name="addressRadio" value="${add_id}" data-default="${editDefault.checked}" ${editDefault.checked ? 'checked' : ''}>
            <span><b>${name}</b> | ${phone}<br>${address}<br></span>
            <a href="#" class="modal-address-update">Cập nhật</a>
        `;
            document.querySelector('.modal-address-list').appendChild(newLabel);
            bindUpdateButtons(); // Gán lại sự kiện cho nút "Cập nhật" mới
        }
        closeEditAddressModal();
    };

    function saveEditAddress() {
        // Xóa các thông báo lỗi cũ
        document.querySelectorAll('.form-group .error-message').forEach(error => error.remove());
        document.querySelectorAll('.form-group input, .form-group textarea').forEach(field => {
            field.classList.remove('is-invalid');
        });

        let isValid = true;

        // Validate tên
        if (!editName.value.trim()) {
            showFieldError(editName, 'Vui lòng nhập họ và tên');
            isValid = false;
        }

        // Validate số điện thoại
        if (!editPhone.value.trim()) {
            showFieldError(editPhone, 'Vui lòng nhập số điện thoại');
            isValid = false;
        } else if (!/^[0-9]{10,11}$/.test(editPhone.value.trim())) {
            showFieldError(editPhone, 'Số điện thoại không hợp lệ');
            isValid = false;
        }

        // Validate địa chỉ
        if (!editAddress.value.trim()) {
            showFieldError(editAddress, 'Vui lòng nhập địa chỉ');
            isValid = false;
        }

        // Nếu validation thất bại, không gửi form
        if (!isValid) {
            return;
        }

        const formData = new FormData(editForm);
        const add_id = formData.get('add_id');

        let defaultValue = 0;
        if (editDefault.disabled) {
            // Nếu checkbox bị disable, kiểm tra lý do
            const addressCount = document.querySelectorAll('.modal-address-item').length;
            if (addressCount === 0) {
                // Không có địa chỉ nào: địa chỉ đầu tiên sẽ là mặc định
                defaultValue = 1;
            } else if (add_id) {
                // Đang edit địa chỉ mặc định: giữ nguyên là mặc định
                defaultValue = 1;
            }
        } else {
            // Checkbox không bị disable: lấy giá trị từ form
            defaultValue = formData.get('default') === 'on' ? 1 : 0;
        }

        const data = {
            add_id: add_id,
            name: formData.get('name'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            default: defaultValue,
            _token: '<?php echo e(csrf_token()); ?>'
        };

        fetch(editForm.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Toastify({
                    text: data.message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#4CAF50",
                    close: true
                }).showToast();

                // Reload trang để cập nhật danh sách địa chỉ
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            Toastify({
                text: error.message || 'Có lỗi xảy ra',
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#EF4444",
                close: true
            }).showToast();
        });
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');

        const errorMessage = document.createElement('span');
        errorMessage.className = 'error-message';
        errorMessage.textContent = message;
        errorMessage.style.color = '#dc3545';
        errorMessage.style.fontSize = '12px';
        errorMessage.style.marginTop = '4px';
        errorMessage.style.display = 'block';

        field.parentElement.appendChild(errorMessage);
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/checkout/checkout.blade.php ENDPATH**/ ?>