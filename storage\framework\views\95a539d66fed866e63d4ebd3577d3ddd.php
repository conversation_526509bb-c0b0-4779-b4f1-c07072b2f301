<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['count' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['count' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php for($i = 0;  $i < $count; $i++): ?>
    <div class="grid gap-2">
        <div class="shimmer h-6 w-32 max-md:h-5"></div>

        <div class="shimmer mb-8 h-12 w-full rounded-lg max-md:-mt-1 max-md:h-10"></div>
    </div>
<?php endfor; ?><?php /**PATH /var/www/html/packages/Webkul/Shop/src/Resources/views/components/shimmer/form/control-group/index.blade.php ENDPATH**/ ?>