
<?php
    // <PERSON><PERSON>y các tham số từ include
    $productId = $product->id ?? null;
    $productName = $product->name ?? '';
    $productImage = $product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png');
    $productDiscount = $product->discount ?? 0;
    $productPrice = $product->price ?? 0;
    $productOriginalPrice = $product->original_price ?? 0;
    $productVisiblePrice = $product->visible_price ?? false;
    $productHasDiscount = $product->has_discount ?? false;

    // Kiểm tra user đăng nhập
    $isLoggedIn = Auth::guard('customer')->check();

    // Logic hiển thị
    $showDiscount = $productDiscount && $productVisiblePrice && $isLoggedIn;
    $showPrice = $productPrice && $productVisiblePrice;
    $showOriginalPrice = $productHasDiscount && $isLoggedIn && $showPrice;
    $showCurrentPrice = $isLoggedIn && $showPrice;

    // Button text logic
    if ($isLoggedIn) {
        $buttonText = ($productPrice && $productVisiblePrice) ? 'Đặt ngay' : 'Báo giá';
    } else {
        $buttonText = ($productPrice && $productVisiblePrice) ? 'Đăng nhập để mua' : 'Đăng nhập để báo giá';
    }
?>

<style>
.product-card-quantity {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 400px;
}

.product-card-quantity .product-image-container {
    flex-shrink: 0;
}

.product-card-quantity .product-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-card-quantity .product-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    height: 2.8em;
}

/* Quantity Controls Styles */
.qty-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0;
    margin: 8px 0;
}

.qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f3f4f6;
    color: #f26522;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.qty-btn:hover {
    background: #f26522;
    color: #fff;
}

.qty-input {
    width: 48px;
    text-align: center;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    margin: 0 2px;
    padding: 0 4px;
    height: 32px;
    box-sizing: border-box;
    background: #fff;
}

/* Hide number input spinners */
.qty-input::-webkit-outer-spin-button,
.qty-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.qty-input[type=number] {
    -moz-appearance: textfield;
}
</style>

<div class="bg-white rounded-lg border border-gray-300 p-3 product-card-quantity">
    <a href="<?php echo e(route('product_detail', ['productId' => $productId])); ?>" class="product-link flex flex-col h-full">
        <div class="relative product-image-container">
            <img src="<?php echo e($productImage); ?>" alt="Product" class="w-full h-48 object-cover rounded-lg">
            <?php if($showDiscount): ?>
                <span class="discount absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-sm rounded">-<?php echo e($productDiscount); ?>%</span>
            <?php endif; ?>
        </div>
        <div class="mt-3 product-content">
            <h3 class="font-semibold text-gray-800 mb-2 product-title"><?php echo e($productName); ?></h3>
            <div class="product-price-section">
                <?php if($showPrice): ?>
                    <?php if($isLoggedIn): ?>
                        <?php if($showOriginalPrice): ?>
                            
                            <p class="text-gray-500 line-through text-sm"><?php echo e(number_format($productOriginalPrice)); ?>đ</p>
                            <p class="text-[#F06F22] font-semibold"><?php echo e(number_format($productPrice)); ?>đ</p>
                        <?php else: ?>
                            
                            <div class="text-sm">&nbsp;</div>
                            <p class="text-[#F06F22] font-semibold"><?php echo e(number_format($productPrice)); ?>đ</p>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="h-[40px]"></div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="h-[40px]"></div>
                <?php endif; ?>
            </div>
        </div>
    </a>

    
    <div class="qty-wrapper">
        <button type="button" class="qty-btn qty-btn-minus" data-product-id="<?php echo e($productId); ?>">−</button>
        <input type="number" class="qty-input" name="quantity_<?php echo e($productId); ?>" min="1" value="1" data-product-id="<?php echo e($productId); ?>">
        <button type="button" class="qty-btn qty-btn-plus" data-product-id="<?php echo e($productId); ?>">+</button>
    </div>

    
    <button class="mt-2 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors w-full product-quantity-btn"
            data-product-id="<?php echo e($productId); ?>"
            data-has-visible-price="<?php echo e($productVisiblePrice ? 'true' : 'false'); ?>"
            data-is-logged-in="<?php echo e($isLoggedIn ? 'true' : 'false'); ?>">
        <?php echo e($buttonText); ?>

    </button>
</div>


<?php /**PATH /var/www/html/resources/themes/medical/views/common/product_card_quantity.blade.php ENDPATH**/ ?>