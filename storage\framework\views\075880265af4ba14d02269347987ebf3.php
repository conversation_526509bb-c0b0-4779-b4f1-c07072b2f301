<?php
    $parentClass = $parentClass ?? '';
    $childClass = $childClass ?? '';
    $perPage = $perPage ?? 10;
    $btnTextMore = $btnTextMore ?? 'Xem thêm';
    $btnTextLess = $btnTextLess ?? 'Thu nhỏ';
    $btnId = 'load-more-btn-' . uniqid();
?>

<button type="button"
        id="<?php echo e($btnId); ?>"
        class="btn load-more-btn"
        data-parent="<?php echo e($parentClass); ?>"
        data-child="<?php echo e($childClass); ?>"
        data-perpage="<?php echo e($perPage); ?>"
        data-more="<?php echo e($btnTextMore); ?>"
        data-less="<?php echo e($btnTextLess); ?>">
    <?php echo e($btnTextMore); ?>

</button>

<?php $__env->startPush('scripts'); ?>

<style>
    .load-more-btn{
        display: flex;
        margin-left: auto;
        margin-right: auto;
        margin-top: 3%;
        font-size: 20px;
        color: #FF6B00;
        border: solid 2px #FF6B00;
        border-radius: 30px;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .load-more-btn:hover{
        color: white;
        background-color: #FF6B00;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const btn = document.getElementById('<?php echo e($btnId); ?>');
    if (!btn) return;

    const parentClass = btn.getAttribute('data-parent');
    const childClass = btn.getAttribute('data-child');
    const perPage = parseInt(btn.getAttribute('data-perpage'));
    const btnTextMore = btn.getAttribute('data-more');
    const btnTextLess = btn.getAttribute('data-less');
    const parent = document.querySelector('.' + parentClass);
    if (!parent) return;
    const items = parent.querySelectorAll('.' + childClass);

    let showing = perPage;

    // Ẩn các item thừa khi load trang
    items.forEach((item, idx) => {
        item.style.display = (idx < perPage) ? '' : 'none';
    });

    btn.addEventListener('click', function() {
        if (btn.innerText === btnTextMore) {
            // Hiện thêm perPage item
            for (let i = showing; i < showing + perPage && i < items.length; i++) {
                items[i].style.display = '';
            }
            showing += perPage;
            if (showing >= items.length) {
                btn.innerText = btnTextLess;
            }
        } else {
            // Thu nhỏ về chỉ còn perPage item
            for (let i = perPage; i < items.length; i++) {
                items[i].style.display = 'none';
            }
            showing = perPage;
            btn.innerText = btnTextMore;
        }
    });

    // Ẩn nút nếu không đủ item
    if (items.length <= perPage) {
        btn.style.display = 'none';
    }
});
</script>
<?php $__env->stopPush(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/common/view_more.blade.php ENDPATH**/ ?>