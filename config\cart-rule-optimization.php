<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Cart Rule Performance Optimization Settings
    |--------------------------------------------------------------------------
    |
    | These settings control various performance optimizations for cart rules
    | to prevent timeout issues when loading large datasets.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Control caching behavior for cart rule condition attributes.
    |
    */
    'cache' => [
        'enabled' => env('CART_RULE_CACHE_ENABLED', true),
        'ttl' => env('CART_RULE_CACHE_TTL', 3600), // 1 hour in seconds
        'key' => 'cart_rule_condition_attributes',
    ],

    /*
    |--------------------------------------------------------------------------
    | Lazy Loading Settings
    |--------------------------------------------------------------------------
    |
    | Control which data should be loaded lazily via AJAX.
    |
    */
    'lazy_loading' => [
        'enabled' => env('CART_RULE_LAZY_LOADING_ENABLED', true),
        'categories' => env('CART_RULE_LAZY_LOAD_CATEGORIES', true),
        'attributes' => env('CART_RULE_LAZY_LOAD_ATTRIBUTES', true),
        'attribute_options_threshold' => env('CART_RULE_ATTRIBUTE_OPTIONS_THRESHOLD', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Timeout Settings
    |--------------------------------------------------------------------------
    |
    | Control timeout and memory limits for cart rule pages.
    |
    */
    'timeout' => [
        'max_execution_time' => env('CART_RULE_MAX_EXECUTION_TIME', 300), // 5 minutes
        'memory_limit' => env('CART_RULE_MEMORY_LIMIT', '512M'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Optimization
    |--------------------------------------------------------------------------
    |
    | Control database query optimizations.
    |
    */
    'database' => [
        'select_only_needed_columns' => env('CART_RULE_SELECT_ONLY_NEEDED', true),
        'limit_eager_loading' => env('CART_RULE_LIMIT_EAGER_LOADING', true),
    ],
];
