<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cost.css', 'resources/themes/medical/css/input-focus.css']); ?>

<div class="quick-order-container">
    <h1 class="quick-order-title">Báo giá</h1>
    <div class="quick-order-search-section">
        <label class="quick-order-search-label">Tìm kiếm thuốc</label>
        <div class="quick-order-search-bar">
            <input type="text" class="quick-order-search-input" placeholder="Nhậ<PERSON> tên thuốc, hoạt chất, công dụng...">
            <button class="quick-order-search-btn">Tìm kiếm</button>
        </div>
    </div>
    <div class="quick-order-content">
        <!-- Sidebar -->
        <div class="quick-order-sidebar">
            <div class="quick-order-filter">
                <div class="quick-order-filter-title"><PERSON>h mục</div>
                <div class="quick-order-filter-list scrollable-list">
                    <label><input type="checkbox"> Thuốc kê đơn</label>
                    <label><input type="checkbox"> Thuốc không kê đơn</label>
                    <label><input type="checkbox"> Thực phẩm chức năng</label>
                    <label><input type="checkbox"> Thiết bị y tế</label>
                    <label><input type="checkbox"> Dụng cụ y tế</label>
                    <label><input type="checkbox"> Vật tư tiêu hao</label>
                    <label><input type="checkbox"> Dược mỹ phẩm</label>
                    <label><input type="checkbox"> Khác</label>
                </div>
            </div>
            <div class="quick-order-filter">
                <div class="quick-order-filter-title">Thương hiệu</div>
                <div class="quick-order-filter-list scrollable-list">
                    <label><input type="checkbox"> Thương hiệu 1</label>
                    <label><input type="checkbox"> Thương hiệu 2</label>
                    <label><input type="checkbox"> Thương hiệu 3</label>
                    <label><input type="checkbox"> Thương hiệu 4</label>
                    <label><input type="checkbox"> Thương hiệu 5</label>
                    <label><input type="checkbox"> Thương hiệu 6</label>
                    <label><input type="checkbox"> Thương hiệu 7</label>
                    <label><input type="checkbox"> Thương hiệu 8</label>
                </div>
            </div>
        </div>
        <!-- Product List -->
        <div class="quick-order-products">
            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

            <div class="quick-order-product-card" title="<?php echo e($product->name); ?>">
                <a href="<?php echo e(route('product_detail', ['productId' => $product->id])); ?>">
                    <div class="quick-order-product-img">
                        <img src="<?php echo e($product->images->first() ? asset('storage/' . $product->images->first()->path) : asset('images/product.png')); ?>" alt="<?php echo e($product->name); ?>">
                    </div>
                    <div class="quick-order-product-name"><?php echo e($product->name); ?></div>
                    <div class="quick-order-product-brand">Thương hiệu: <?php echo e($product->brand ?? 'N/A'); ?></div>
                    <div class="quick-order-product-unit">Đơn vị: <?php echo e($product->unit ?? 'N/A'); ?></div>
                </a>
                <input type="number" class="quick-order-qty-input" name="quantity_<?php echo e($product->id); ?>" min="1" value="1" style="width: 60px; margin-bottom: 8px; text-align: center;" />
                <button class="quick-order-add-btn" data-product-id="<?php echo e($product->id); ?>">Báo giá</button>
            </div>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

    </div>
    <div class="quick-order-pagination">
        <button class="quick-order-page-btn" data-page="first">&laquo;</button>
        <button class="quick-order-page-btn active">1</button>
        <button class="quick-order-page-btn">2</button>
        <button class="quick-order-page-btn">3</button>
        <button class="quick-order-page-btn" data-page="last">&raquo;</button>
    </div>
</div>
<script>

document.addEventListener('DOMContentLoaded', function() {
    // Tìm tất cả input số lượng
    document.querySelectorAll('.quick-order-qty-input').forEach(function(input) {
        // Tạo wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'quick-order-qty-wrapper';

        // Tạo nút -
        const btnMinus = document.createElement('button');
        btnMinus.type = 'button';
        btnMinus.className = 'quick-order-qty-btn quick-order-qty-btn-minus';
        btnMinus.textContent = '−';

        // Tạo nút +
        const btnPlus = document.createElement('button');
        btnPlus.type = 'button';
        btnPlus.className = 'quick-order-qty-btn quick-order-qty-btn-plus';
        btnPlus.textContent = '+';

        // Đưa input vào wrapper
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(btnMinus);
        wrapper.appendChild(input);
        wrapper.appendChild(btnPlus);

        // Xử lý sự kiện
        btnMinus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            if (val > (parseInt(input.min) || 1)) {
                input.value = val - 1;
                input.dispatchEvent(new Event('change'));
            }
        });
        btnPlus.addEventListener('click', function() {
            let val = parseInt(input.value) || 1;
            let max = input.max ? parseInt(input.max) : Infinity;
            if (val < max) {
                input.value = val + 1;
                input.dispatchEvent(new Event('change'));
            }
        });
    });
});

    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.quick-order-pagination').forEach(function(pagination) {
            pagination.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-order-page-btn')) {
                    const buttons = Array.from(pagination.querySelectorAll('.quick-order-page-btn'))
                        .filter(btn => !btn.hasAttribute('data-page'));
                    // Nếu bấm <<
                    if (e.target.getAttribute('data-page') === 'first') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[0].classList.add('active');
                    }
                    // Nếu bấm >>
                    else if (e.target.getAttribute('data-page') === 'last') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[buttons.length - 1].classList.add('active');
                    }
                    // Nếu bấm số trang
                    else {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');
                    }
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/cost/cost.blade.php ENDPATH**/ ?>