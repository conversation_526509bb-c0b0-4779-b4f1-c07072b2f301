<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/brands.css']); ?>
<div class="brands-container">
    <h2 class="brands-title">Tất cả các thương hiệu</h2>
    <div class="brands-list">
        <?php $__empty_1 = true; $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="brand-card cursor-pointer hover:shadow-lg transition-shadow duration-300" onclick="window.location.href='/brands_detail/<?php echo e($brand->id); ?>'">
                <div class="brand-img">
                    <?php if($brand->brand_image): ?>
                        <img src="<?php echo e(asset('storage/' . $brand->brand_image)); ?>" alt="<?php echo e($brand->brand_name); ?>">
                    <?php else: ?>
                        <img src="/images/default-brand.png" alt="<?php echo e($brand->brand_name); ?>">
                    <?php endif; ?>
                </div>
                <div class="brand-name"><?php echo e($brand->brand_name); ?></div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="no-brands">
                Chưa có thương hiệu nào.
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="ingredient-pagination">
    <?php echo e($brands->links('vendor.pagination.custom')); ?>

</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/brands/brands.blade.php ENDPATH**/ ?>