<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cost_detail.css']); ?>
<div class="checkout-container">
    <!-- Cột trái: Thông tin báo giá -->
    <div class="checkout-left">
        <div class="checkout-section">
            <h2 class="section-title">Báo giá #<?php echo e($quote->id); ?></h2>
            <!-- product -->
            <?php $__currentLoopData = $quote->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="product-summary">
                <img src="<?php echo e($item->product && $item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>"
                    alt="Sản phẩm" class="product-img">
                <div class="product-info">
                    <div class="product-name"><?php echo e($item->name); ?></div>
                    <div class="product-variant"><?php echo e($item->product_type ?? 'Mặc định'); ?></div>
                </div>
                <div class="product-qty">x<?php echo e($item->quantity); ?></div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <?php if($quote->items->count() > 5): ?>
            <div class="quick-order-pagination">
                <button class="quick-order-page-btn" data-page="first">&laquo;</button>
                <button class="quick-order-page-btn active">1</button>
                <button class="quick-order-page-btn">2</button>
                <button class="quick-order-page-btn">3</button>
                <button class="quick-order-page-btn" data-page="last">&raquo;</button>
            </div>
            <?php endif; ?>

            <?php if($quote->customer_note): ?>
            <div class="checkout-note">
                <label for="note">Ghi chú</label>
                <textarea id="note" readonly><?php echo e($quote->customer_note); ?></textarea>
            </div>
            <?php endif; ?>

            <div class="checkout-note">
                <div class="address-title">Trạng thái báo giá</div>
                <p><?php echo e(ucfirst($quote->status)); ?></p>
            </div>
            <div class="checkout-note" style="margin-top: 2%;">
                <div class="address-title">Ngày gửi yêu cầu</div>
                <p><?php echo e($quote->updated_at->format('d/m/Y H:i')); ?></p>
            </div>
        </div>

        <div class="checkout-section">
            <div class="address-info-row">
                <div>
                    <div class="address-title">Thông tin người yêu cầu</div>
                    <div class="address-user">
                        <span class="address-name"><?php echo e($quote->customer_name); ?></span>
                        <span class="address-phone">| <?php echo e($quote->customer_phone ?? 'Không có'); ?></span>
                    </div>
                    <div class="address-detail">
                        <?php echo e($quote->customer_email); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cột phải: Thông tin thêm -->
    <div class="checkout-right">
        <div class="checkout-summary">
            <div class="summary-row summary-promo">
                <span>Trạng thái</span>
                <span class="badge <?php echo e($quote->status == 'submitted' ? 'badge-warning' : 'badge-success'); ?>">
                    <?php echo e($quote->status == 'submitted' ? 'Đang xử lý' : ucfirst($quote->status)); ?>

                </span>
            </div>
            <div class="summary-row summary-pxu">
                <span>Ngày tạo</span>
                <span><?php echo e($quote->created_at->format('d/m/Y H:i')); ?></span>
            </div>
            <div class="summary-row summary-vat">
                <span>Ngày cập nhật</span>
                <span><?php echo e($quote->updated_at->format('d/m/Y H:i')); ?></span>
            </div>
            <div class="summary-row summary-vat">
                <span>Số lượng sản phẩm</span>
                <span><?php echo e($quote->items->count()); ?></span>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.quick-order-pagination').forEach(function(pagination) {
            pagination.addEventListener('click', function(e) {
                if (e.target.classList.contains('quick-order-page-btn')) {
                    const buttons = Array.from(pagination.querySelectorAll('.quick-order-page-btn'))
                        .filter(btn => !btn.hasAttribute('data-page'));
                    // Nếu bấm <<
                    if (e.target.getAttribute('data-page') === 'first') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[0].classList.add('active');
                    }
                    // Nếu bấm >>
                    else if (e.target.getAttribute('data-page') === 'last') {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        if (buttons.length) buttons[buttons.length - 1].classList.add('active');
                    }
                    // Nếu bấm số trang
                    else {
                        buttons.forEach(btn => btn.classList.remove('active'));
                        e.target.classList.add('active');
                    }
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/cost_detail/quote_detail.blade.php ENDPATH**/ ?>