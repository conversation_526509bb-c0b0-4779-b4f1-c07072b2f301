<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/news_detail.css']); ?>

<div class="container mx-auto px-4 py-8">
<div class="news-detail-container">
    <div class="news-detail-header">
        <h1 class="news-detail-title">
            <?php echo e($news->title); ?>

        </h1>
        <div class="news-detail-meta">
            <span class="news-detail-date"><?php echo e($news->published_at ? $news->published_at->format('d/m/Y') : $news->created_at->format('d/m/Y')); ?></span>
            <span class="news-detail-author">Bởi <?php echo e($news->author ?? 'Admin'); ?></span>
            <?php if($news->category): ?>
                <span class="news-detail-category"><?php echo e($news->category->name); ?></span>
            <?php endif; ?>
        </div>
    </div>
    <?php if($news->featured_image): ?>
        <div class="news-detail-image">
            <img src="<?php echo e($news->featured_image_url); ?>" alt="<?php echo e($news->title); ?>">
        </div>
    <?php endif; ?>
    <div class="news-detail-content">
        <?php echo $news->content; ?>

    </div>

    <!-- Related News Section -->
    <?php if($relatedNews->count() > 0): ?>
        <div class="related-news-section">
            <h2 class="related-news-title">Các bài viết liên quan</h2>
            <div class="related-news-slider-wrapper">
                <div class="container related-news-slider" id="relatedNewsSlider">
                    <?php $__currentLoopData = $relatedNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12 col-md-4 related-news-card" onclick="window.location.href='<?php echo e(route('news.detail', $relatedItem->slug)); ?>'" style="cursor: pointer;">
                            <img src="<?php echo e($relatedItem->featured_image_url); ?>" alt="<?php echo e($relatedItem->title); ?>">
                            <div class="related-news-tag"><?php echo e($relatedItem->category ? $relatedItem->category->name : 'Tin tức'); ?></div>
                            <div class="related-news-headline"><?php echo e(Str::limit($relatedItem->title, 50)); ?></div>
                            <div class="related-news-desc"><?php echo e(Str::limit($relatedItem->excerpt, 80)); ?></div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('relatedNewsSlider');
    const prevBtn = document.querySelector('.related-news-btn.prev');
    const nextBtn = document.querySelector('.related-news-btn.next');
    
    if (slider) {
        const cards = slider.querySelectorAll('.related-news-card');
        if (cards.length > 0) {
            const cardWidth = cards[0].offsetWidth + 18; // 18px là gap
            const visibleCards = Math.floor(slider.parentElement.offsetWidth / cardWidth);
            let position = 0;

            function updateSlider() {
                slider.style.transform = `translateX(-${position * cardWidth}px)`;
                // Ẩn/hiện nút
                if (position === 0) {
                    prevBtn.style.display = 'none';
                } else {
                    prevBtn.style.display = '';
                }
                if (position >= cards.length - visibleCards) {
                    nextBtn.style.display = 'none';
                } else {
                    nextBtn.style.display = '';
                }
            }

            prevBtn.addEventListener('click', function() {
                if (position > 0) {
                    position--;
                    updateSlider();
                }
            });

            nextBtn.addEventListener('click', function() {
                if (position < cards.length - visibleCards) {
                    position++;
                    updateSlider();
                }
            });

            // Khởi tạo
            updateSlider();

            // Responsive: cập nhật lại khi resize
            window.addEventListener('resize', function() {
                location.reload();
            });
        }
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/news_detail/news_detail.blade.php ENDPATH**/ ?>