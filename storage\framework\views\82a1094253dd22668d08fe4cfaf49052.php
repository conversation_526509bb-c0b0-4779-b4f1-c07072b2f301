<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/news.css']); ?>
<!-- news -->
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h2 class="text-xl sm:text-2xl font-bold">Tin tức</h2>
    </div>

<!-- News Categories -->
<div class="news-categories">
    <div class="category-tag <?php echo e(!$selectedCategory ? 'active' : ''); ?>" onclick="window.location.href='<?php echo e(route('news')); ?>'">
        Tất cả
    </div>
    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="category-tag <?php echo e($selectedCategory && $selectedCategory->slug == $category->slug ? 'active' : ''); ?>"
             onclick="window.location.href='<?php echo e(route('news', ['category' => $category->slug])); ?>'">
            <?php echo e($category->name); ?>

        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<?php if($featuredNews): ?>
    <!-- Layout with featured news (3 columns) -->
    <div class="news-grid">
        <!-- Main News -->
        <div class="main-news border border-gray-300 rounded-lg" onclick="window.location.href='<?php echo e(route('news.detail', $featuredNews->slug)); ?>'" style="cursor: pointer;">
            <img src="<?php echo e($featuredNews->featured_image_url); ?>" alt="<?php echo e($featuredNews->title); ?>">
            <div class="news-content">
                <span class="news-tag"><?php echo e($featuredNews->category ? $featuredNews->category->name : 'Tin tức'); ?></span>
                <h3 class="text-xl font-semibold mt-2"><?php echo e($featuredNews->title); ?></h3>
                <p class="mt-2"><?php echo e($featuredNews->excerpt); ?></p>
            </div>
        </div>

        <!-- Column 2 -->
        <div class="flex flex-col gap-8 h-[85%]">
            <?php $newsChunk1 = $news->slice(0, 3) ?>
            <?php $__empty_1 = true; $__currentLoopData = $newsChunk1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="small-news border border-gray-300 rounded-lg" onclick="window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'" style="cursor: pointer;">
                    <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>">
                    <div class="news-content">
                        <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                        <h3><?php echo e(Str::limit($newsItem->title, 80)); ?></h3>
                        <p><?php echo e(Str::limit($newsItem->excerpt, 150)); ?></p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- No news in this column -->
            <?php endif; ?>
        </div>

        <!-- Column 3 -->
        <div class="flex flex-col gap-8 h-[85%]">
            <?php $newsChunk2 = $news->slice(3, 3) ?>
            <?php $__empty_1 = true; $__currentLoopData = $newsChunk2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="small-news border border-gray-300 rounded-lg" onclick="window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'" style="cursor: pointer;">
                    <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>">
                    <div class="news-content">
                        <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                        <h3><?php echo e(Str::limit($newsItem->title, 80)); ?></h3>
                        <p><?php echo e(Str::limit($newsItem->excerpt, 150)); ?></p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- No news in this column -->
            <?php endif; ?>
        </div>
    </div>
<?php else: ?>
    <!-- Layout without featured news (responsive grid) -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="small-news border border-gray-300 rounded-lg hover:shadow-lg transition-all duration-300 cursor-pointer" onclick="window.location.href='<?php echo e(route('news.detail', $newsItem->slug)); ?>'">
                <img src="<?php echo e($newsItem->featured_image_url); ?>" alt="<?php echo e($newsItem->title); ?>" class="w-full h-48 object-cover rounded-t-lg">
                <div class="news-content p-4">
                    <span class="news-tag"><?php echo e($newsItem->category ? $newsItem->category->name : 'Tin tức'); ?></span>
                    <h3 class="mt-2 font-semibold text-gray-800 line-clamp-2"><?php echo e($newsItem->title); ?></h3>
                    <p class="mt-2 text-gray-600 text-sm line-clamp-3"><?php echo e($newsItem->excerpt); ?></p>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-8">
                <p class="text-gray-500">Không có tin tức nào trong danh mục này.</p>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>

    <!-- Pagination -->
    <div class="ingredient-pagination">
        <?php echo e($news->links('vendor.pagination.custom')); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/news/news.blade.php ENDPATH**/ ?>