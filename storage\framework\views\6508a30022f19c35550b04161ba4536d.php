<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/illness_detail.css']); ?>
<div class="illness-detail-layout">
    <!-- Sidebar đề mục -->
    <nav class="illness-detail-sidebar" id="illness-sidebar">
        <ul>
            <li class="sidebar-item active"><a href="#tong-quan">Tổng quan chung</a></li>
            <li class="sidebar-item"><a href="#trieu-chung">Tri<PERSON>u chứng</a></li>
            <li class="sidebar-item"><a href="#nguyen-nhan">Nguyên nhân</a></li>
            <li class="sidebar-item"><a href="#doi-tuong">Đ<PERSON>i tượng nguy cơ</a></li>
            <li class="sidebar-item"><a href="#chan-doan"><PERSON><PERSON><PERSON> đ<PERSON></a></li>
            <li class="sidebar-item"><a href="#phong-ngua">Phòng ngừa bệnh</a></li>
            <li class="sidebar-item"><a href="#dieu-tri">Cách điều trị</a></li>
        </ul>
    </nav>

    <!-- Nội dung chi tiết -->
    <div class="illness-detail-content">
        <div class="illness-detail-date"><?php echo e($illness->created_at->format('d/m/Y')); ?></div>
        <h1 class="illness-detail-title"><?php echo e($illness->name); ?></h1>
        <div class="illness-detail-tags">
            <?php $__currentLoopData = $illness->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('medical.illness_category.show', $category->slug)); ?>"><?php echo e($category->name); ?></a>
                <?php if($index < $illness->categories->count() - 1): ?> • <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php if($illness->bodyParts->count() > 0 && $illness->categories->count() > 0): ?> • <?php endif; ?>
            <?php $__currentLoopData = $illness->bodyParts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $bodyPart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('medical.illness_category', ['body_part' => $bodyPart->slug])); ?>"><?php echo e($bodyPart->name); ?></a>
                <?php if($index < $illness->bodyParts->count() - 1): ?> • <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <section id="tong-quan" class="illness-section">
            <h2><?php echo e($illness->name); ?> là gì?</h2>
            <?php if($illness->overview): ?>
                <div style="white-space: pre-line;"><?php echo $illness->overview; ?></div>
            <?php elseif($illness->description): ?>
                <p><?php echo e($illness->description); ?></p>
            <?php else: ?>
                <p>Thông tin tổng quan về <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
            <?php if($illness->image_url): ?>
                <img src="<?php echo e($illness->image_url); ?>" alt="<?php echo e($illness->name); ?>" class="illness-detail-img">
            <?php endif; ?>
        </section>

        <section id="trieu-chung" class="illness-section">
            <h2>Triệu chứng</h2>
            <?php if($illness->symptoms): ?>
                <div style="white-space: pre-line;"><?php echo $illness->symptoms; ?></div>
            <?php else: ?>
                <p>Thông tin về triệu chứng của <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <section id="nguyen-nhan" class="illness-section">
            <h2>Nguyên nhân</h2>
            <?php if($illness->causes): ?>
                <div style="white-space: pre-line;"><?php echo $illness->causes; ?></div>
            <?php else: ?>
                <p>Thông tin về nguyên nhân gây <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <section id="doi-tuong" class="illness-section">
            <h2>Đối tượng nguy cơ</h2>
            <?php if($illness->risk_groups): ?>
                <div style="white-space: pre-line;"><?php echo $illness->risk_groups; ?></div>
            <?php else: ?>
                <p>Thông tin về đối tượng nguy cơ mắc <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <section id="chan-doan" class="illness-section">
            <h2>Chẩn đoán</h2>
            <?php if($illness->diagnosis): ?>
                <div style="white-space: pre-line;"><?php echo $illness->diagnosis; ?></div>
            <?php else: ?>
                <p>Thông tin về phương pháp chẩn đoán <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <section id="phong-ngua" class="illness-section">
            <h2>Phòng ngừa bệnh</h2>
            <?php if($illness->prevention): ?>
                <div style="white-space: pre-line;"><?php echo $illness->prevention; ?></div>
            <?php else: ?>
                <p>Thông tin về cách phòng ngừa <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <section id="dieu-tri" class="illness-section">
            <h2>Cách điều trị</h2>
            <?php if($illness->treatment): ?>
                <div style="white-space: pre-line;"><?php echo $illness->treatment; ?></div>
            <?php else: ?>
                <p>Thông tin về phương pháp điều trị <?php echo e($illness->name); ?> sẽ được cập nhật sớm.</p>
            <?php endif; ?>
        </section>

        <?php if(isset($relatedIllnesses) && $relatedIllnesses->count() > 0): ?>
        <section class="illness-section">
            <h2>Bệnh liên quan</h2>
            <div class="related-illnesses">
                <?php $__currentLoopData = $relatedIllnesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedIllness): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('medical.illness_detail', $relatedIllness->slug)); ?>" class="related-illness-card">
                    <img src="<?php echo e($relatedIllness->image_url ?? '/images/default-illness.png'); ?>" alt="<?php echo e($relatedIllness->name); ?>">
                    <span><?php echo e($relatedIllness->name); ?></span>
                </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </section>
        <?php endif; ?>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sections = Array.from(document.querySelectorAll('.illness-section'));
        const sidebarItems = document.querySelectorAll('.illness-detail-sidebar .sidebar-item');
        const sidebarLinks = document.querySelectorAll('.illness-detail-sidebar .sidebar-item a');
        let userHasClicked = false; // Track if user has manually clicked any item

        // Scroll đến mục khi click
        sidebarLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Mark that user has clicked - this will disable auto-scroll tracking
                userHasClicked = true;

                // Remove active class from all sidebar items
                sidebarItems.forEach(item => item.classList.remove('active'));

                // Add active class to clicked item
                this.parentElement.classList.add('active');

                // Scroll to target section
                const id = this.getAttribute('href').replace('#', '');
                const section = document.getElementById(id);
                if (section) {
                    window.scrollTo({
                        top: section.getBoundingClientRect().top + window.scrollY - 80, // 80px offset for sticky header
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Active sidebar khi scroll (chỉ hoạt động khi user chưa click gì)
        function onScroll() {
            // If user has clicked any item, stop auto-updating active state
            if (userHasClicked) return;

            let scrollPos = window.scrollY + 100; // offset for sticky header
            let current = 0;

            // Find the current section based on scroll position
            for (let i = 0; i < sections.length; i++) {
                const section = sections[i];
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;

                // Check if we're in this section
                if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                    current = i;
                    break;
                } else if (scrollPos >= sectionTop) {
                    current = i; // Keep updating current as we scroll down
                }
            }

            // Update active state
            sidebarItems.forEach(item => item.classList.remove('active'));
            if (sidebarItems[current]) {
                sidebarItems[current].classList.add('active');
            }
        }

        window.addEventListener('scroll', onScroll);

        // Set initial active state (only if user hasn't clicked anything yet)
        setTimeout(() => {
            if (!userHasClicked) {
                onScroll();
            }
        }, 100);
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/illness_detail/illness_detail.blade.php ENDPATH**/ ?>