<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/cart.css', 'resources/themes/medical/css/input-focus.css']); ?>

<style>
/* Custom styles for toast notifications */
.toast-notification {
    font-family: 'Be Vietnam Pro', sans-serif !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.toast-notification .toast-close {
    color: white !important;
    opacity: 0.8 !important;
}

.toast-notification .toast-close:hover {
    opacity: 1 !important;
}

/* Fix cart count alignment */
.cart-count, .quote-count {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    line-height: 1 !important;
}
</style>

<?php echo $__env->make('medical::common.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<div class="cart-container">
    <div class="cart-main">
        <div class="cart-header">
            <h1 class="cart-title">Giỏ hàng</h1>
            <button id="delete-selected" class="delete-btn" disabled>Xóa</button>
        </div>

        <div class="cart-table">
            <div class="cart-table-header">
                <div class="cart-checkbox-cell">
                    <input type="checkbox" id="select-all" class="cart-checkbox">
                    <label for="select-all">Sản phẩm</label>
                </div>
                <div class="cart-price-cell">Đơn giá</div>
                <div class="cart-quantity-cell">Số lượng</div>
                <div class="cart-total-cell">Thành tiền</div>
                <div class="cart-action-cell">Xóa sản phẩm</div>
            </div>

            <div class="cart-items">
                <?php $__empty_1 = true; $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="cart-item" data-id="<?php echo e($item->id); ?>" data-price="<?php echo e($item->price); ?>">
                    <div class="cart-checkbox-cell">
                        <input type="checkbox" id="item-<?php echo e($item->id); ?>" class="cart-checkbox item-checkbox">
                        <label for="item-<?php echo e($item->id); ?>" class="sr-only">Chọn sản phẩm</label>
                        <div class="cart-product-info">
                            <img src="<?php echo e($item->product->images->first() ? asset('storage/' . $item->product->images->first()->path) : asset('images/product.png')); ?>" alt="<?php echo e($item->name); ?>" class="cart-product-image">
                            <div class="cart-product-details">
                                <h3 class="cart-product-name"><?php echo e($item->name); ?></h3>
                                <?php
                                    $unitTypeName = null;
                                    if (!empty($item->additional['unit_type'])) {
                                        $unitTypeName = DB::table('attribute_options')->where('id', $item->additional['unit_type'])->value('admin_name');
                                    }
                                ?>
                                <div class="cart-product-variant">
                                    <span class="variant-label">Phân loại: </span>
                                    <div class="variant-selector">
                                        <span class="selected-variant"><?php echo e($unitTypeName ?? 'Mặc định'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cart-price-cell">
                        <span class="product-price"><?php echo e(number_format($item->price, 0, ',', '.')); ?>đ</span>
                    </div>
                    <div class="cart-quantity-cell">
                        <div class="quantity-control">
                            <button class="quantity-btn decrease">−</button>
                            <input type="text" class="quantity-input" value="<?php echo e($item->quantity); ?>" readonly>
                            <button class="quantity-btn increase">+</button>
                        </div>
                    </div>
                    <div class="cart-total-cell">
                        <span class="product-total"><?php echo e(number_format($item->total, 0, ',', '.')); ?>đ</span>
                    </div>
                    <div class="cart-action-cell">
                        <button class="delete-item-btn" data-id="<?php echo e($item->id); ?>"><i class="fa-solid fa-trash-can"></i></button>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="empty-cart">
                    <p class="no-products-message">Giỏ hàng của bạn đang trống</p>
                    <a href="/" class="continue-shopping-btn"><strong>Tiếp tục mua sắm</strong></a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="cart-summary">
        <div class="summary-header">
            <h2 class="summary-title">Tạm tính</h2>
        </div>

        <div class="summary-content">
            <div class="summary-total">
                <span class="total-label">Tổng tiền</span>
                <span class="total-value">0 đ</span>
            </div>

            <form id="checkout-form" action="<?php echo e(route('checkout')); ?>" method="GET">
                <input type="hidden" name="selected_items" id="selected-items">
                <button type="submit" id="checkout-btn" class="checkout-btn" disabled>Mua hàng</button>
            </form>
        </div>
    </div>
</div>

<!-- products -->
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Sản phẩm đã xem</h2>
        <!-- <a href="#" class="text-orange-500 hover:text-orange-600">Xem thêm ></a> -->
        <a href="<?php echo e(route('recently_viewed')); ?>" class="more-button mt-4 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors inline-block" style="white-space: nowrap;">
                Xem thêm >
        </a>
    </div>

    <!-- Recently Viewed Products -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $recentlyViewedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php echo $__env->make('medical::common.product_card', [
                    'product' => $product
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">Chưa có sản phẩm nào được xem gần đây</p>
                </div>
            <?php endif; ?>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const checkoutBtn = document.getElementById('checkout-btn');
    const cartItems = document.querySelectorAll('.cart-item');
    const selectAll = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const deleteSelectedBtn = document.getElementById('delete-selected');
    const confirmModal = document.getElementById('confirm-modal');
    const confirmCancel = document.getElementById('confirm-cancel');
    const confirmDelete = document.getElementById('confirm-delete');
    const selectedItemsInput = document.getElementById('selected-items');
    let deleteTarget = null;

    // Khởi tạo tổng tiền ban đầu
    updateSelectedItems();

    // Khởi tạo cart count để đảm bảo CSS đúng
    updateHeaderCartCount();

    // Auto-select all items when page loads
    const startupItemCheckboxes = document.querySelectorAll('.item-checkbox');
    if (startupItemCheckboxes.length > 0) {
        startupItemCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        selectAll.checked = true;
        updateSelectedItems();
    }

    // 1. Hiển thị số sản phẩm được tick trên nút "Mua hàng" và đổi màu khi có tick
    function updateCheckoutBtn() {
        const checkedItems = document.querySelectorAll('.item-checkbox:checked');
        const count = checkedItems.length;

        let text = 'Mua hàng';
        if (count > 0) text += ` (${count})`;
        checkoutBtn.innerText = text;
        checkoutBtn.disabled = count === 0;
        checkoutBtn.classList.toggle('active', count > 0);
    }

    // 2. Xử lý check/uncheck "Sản phẩm" (select all)
    function updateSelectAllCheckbox() {
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (checkedCount === 0) {
            selectAll.checked = false;
            selectAll.indeterminate = false;
        } else if (checkedCount === currentItemCheckboxes.length) {
            selectAll.checked = true;
            selectAll.indeterminate = false;
        } else {
            selectAll.checked = false;
            selectAll.indeterminate = true;
        }
    }

    // 3. Xử lý nút "Xóa" (chỉ bật khi có tick)
    function updateDeleteSelectedBtn() {
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        deleteSelectedBtn.disabled = checkedCount === 0;
    }

    // 4. Cập nhật tổng tiền và danh sách sản phẩm được chọn
    function updateSelectedItems() {
        const selectedItems = [];
        let total = 0;

        // Query lại từ DOM thay vì sử dụng NodeList cũ
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
        
        currentItemCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const itemElement = checkbox.closest('.cart-item');
                if (itemElement) { // Kiểm tra element còn tồn tại
                    const itemId = itemElement.dataset.id;
                    const price = parseFloat(itemElement.dataset.price);
                    const quantity = parseInt(itemElement.querySelector('.quantity-input').value);

                    selectedItems.push(itemId);
                    total += price * quantity;
                }
            }
        });

        selectedItemsInput.value = JSON.stringify(selectedItems);

        // Cập nhật tổng tiền
        document.querySelector('.total-value').textContent = formatPrice(total);
        updateCheckoutBtn();
        updateSelectAllCheckbox();
        updateDeleteSelectedBtn();
    }

    // 5. Sự kiện tick sản phẩm
    const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
    currentItemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedItems);
    });

    // 6. Sự kiện tick "Sản phẩm" (select all)
    selectAll.addEventListener('change', function() {
        const currentItemCheckboxes = document.querySelectorAll('.item-checkbox');
        currentItemCheckboxes.forEach(cb => cb.checked = selectAll.checked);
        updateSelectedItems();
    });

    // 7. Xử lý form submit
    document.getElementById('checkout-form').addEventListener('submit', function(e) {
        const checkedItems = document.querySelectorAll('.item-checkbox:checked');
        if (checkedItems.length === 0) {
            e.preventDefault();
            ModalCommon.showError('Bạn chưa chọn sản phẩm nào');
            return false;
        }

        // Nếu đang cập nhật số lượng thì không cho submit (nút đã bị disable)
        if (isUpdatingQuantity) {
            e.preventDefault();
            return false;
        }

        // Cập nhật selected_items trước khi submit
        const selectedItems = [];
        checkedItems.forEach(checkbox => {
            const itemId = checkbox.closest('.cart-item').dataset.id;
            selectedItems.push(itemId);
        });
        document.getElementById('selected-items').value = JSON.stringify(selectedItems);
    });

    // 8. Xử lý xóa sản phẩm (có modal xác nhận)
    function showConfirmModal(target) {
        if (target === 'selected') {
            // Xóa nhiều sản phẩm - hiển thị modal xác nhận
            const selectedItems = document.querySelectorAll('.item-checkbox:checked');
            if (selectedItems.length === 0) return;

            ModalCommon.showConfirm(
                `Bạn có muốn xóa ${selectedItems.length} sản phẩm đã chọn khỏi giỏ hàng?`,
                function() {
                    deleteSelectedItems();
                }
            );
        } else if (target) {
            // Xóa một sản phẩm - hiển thị modal xác nhận
            const productName = target.querySelector('.cart-product-name').textContent;
            ModalCommon.showConfirm(
                `Bạn có muốn xóa sản phẩm "${productName}" khỏi giỏ hàng?`,
                function() {
                    deleteItem(target.dataset.id);
                }
            );
        }
    }
    function hideConfirmModal() {
        deleteTarget = null;
    }

    // 9. Khi nhấn vào thùng rác
    document.querySelectorAll('.delete-item-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const cartItem = btn.closest('.cart-item');
            showConfirmModal(cartItem);
        });
    });

    // 10. Khi nhấn "Xóa" (trên cùng)
    deleteSelectedBtn.addEventListener('click', function() {
        showConfirmModal('selected');
    });





    // 13. Hàm xóa nhiều sản phẩm
    function deleteSelectedItems() {
        // Lấy danh sách ID của các sản phẩm được chọn
        const selectedItems = document.querySelectorAll('.item-checkbox:checked');
        const itemIds = Array.from(selectedItems).map(cb => {
            const item = cb.closest('.cart-item');
            return item ? item.dataset.id : null;
        }).filter(id => id !== null);

        if (itemIds.length === 0) return;

        // Gọi API xóa nhiều sản phẩm
        fetch('/api/medical/cart/remove-items', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                item_ids: itemIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Xóa các sản phẩm khỏi UI
                selectedItems.forEach(cb => {
                    const item = cb.closest('.cart-item');
                    if (item) item.remove();
                });

                // Hiển thị toast thông báo thành công
                showToast({
                    type: 'success',
                    message: data.message || `Đã xóa ${itemIds.length} sản phẩm khỏi giỏ hàng`
                });

                // Cập nhật số lượng giỏ hàng ở header từ API response
                if (data.cart_count !== undefined) {
                    updateHeaderCartCount(data.cart_count);
                }

                // Kiểm tra nếu giỏ hàng trống, hiển thị thông báo
                if (document.querySelectorAll('.cart-item').length === 0) {
                    const cartItemsContainer = document.querySelector('.cart-items');
                    if (cartItemsContainer) {
                        cartItemsContainer.innerHTML = `
                            <div class="empty-cart">
                                <p class="no-products-message">Giỏ hàng của bạn đang trống</p>
                                <a href="/" class="continue-shopping-btn"><strong>Tiếp tục mua sắm</strong></a>
                            </div>
                        `;
                    }
                }
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra khi xóa sản phẩm'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi xóa sản phẩm'
            });
        })
        .finally(() => {
            hideConfirmModal();
            updateCheckoutBtn();
            updateSelectAllCheckbox();
            updateDeleteSelectedBtn();
            updateSelectedItems();
        });
    }

    // 14. Hàm xóa một sản phẩm
    function deleteItem(itemId) {
        fetch('/api/medical/cart/remove-item', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                item_id: itemId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = document.querySelector(`.cart-item[data-id="${itemId}"]`);
                if (item) {
                    const productName = item.querySelector('.cart-product-name').textContent;
                    item.remove();
                }

                // Hiển thị toast thông báo thành công
                showToast({
                    type: 'success',
                    message: data.message || 'Đã xóa sản phẩm khỏi giỏ hàng'
                });

                // Cập nhật số lượng giỏ hàng ở header từ API response
                if (data.cart_count !== undefined) {
                    updateHeaderCartCount(data.cart_count);
                }

                // Kiểm tra nếu giỏ hàng trống, hiển thị thông báo
                if (document.querySelectorAll('.cart-item').length === 0) {
                    const cartItemsContainer = document.querySelector('.cart-items');
                    if (cartItemsContainer) {
                        cartItemsContainer.innerHTML = `
                            <div class="empty-cart">
                                <p class="no-products-message">Giỏ hàng của bạn đang trống</p>
                                <a href="/" class="continue-shopping-btn"><strong>Tiếp tục mua sắm</strong></a>
                            </div>
                        `;
                    }
                }
            } else {
                showToast({
                    type: 'error',
                    message: data.message || 'Có lỗi xảy ra khi xóa sản phẩm'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra khi xóa sản phẩm'
            });
        })
        .finally(() => {
            hideConfirmModal();
            updateCheckoutBtn();
            updateSelectAllCheckbox();
            updateDeleteSelectedBtn();
            updateSelectedItems();
        });
    }

    // 14. Ẩn modal khi click ra ngoài
    confirmModal.addEventListener('mousedown', function(e) {
        if (e.target === confirmModal) hideConfirmModal();
    });

    // 15. Dropdown variant
    document.querySelectorAll('.cart-product-variant .variant-selector').forEach(selector => {
        selector.addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = selector.querySelector('.variant-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        });
        // Ẩn dropdown khi click ra ngoài
        document.addEventListener('mousedown', function(e) {
            if (!selector.contains(e.target)) {
                const dropdown = selector.querySelector('.variant-dropdown');
                if (dropdown) dropdown.style.display = 'none';
            }
        });
    });

    // Modal báo lỗi
    const errorModal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');
    const errorOk = document.getElementById('error-ok');

    function showErrorModal(message) {
        errorMessage.textContent = message || 'Có lỗi xảy ra';
        errorModal.style.display = 'flex';
    }
    function hideErrorModal() {
        errorModal.style.display = 'none';
    }
    if (errorOk) errorOk.addEventListener('click', hideErrorModal);
    if (errorModal) {
        errorModal.addEventListener('mousedown', function(e) {
            if (e.target === errorModal) hideErrorModal();
        });
    }

    // Hàm debounce để tránh gọi API quá nhiều
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Hàm format giá
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price).replace('VND', 'đ');
    }

    // 16. Hàm cập nhật tổng tiền của 1 item
    function updateItemTotal(cartItem, quantity) {
        const price = parseFloat(cartItem.dataset.price);
        const total = price * quantity;
        cartItem.querySelector('.product-total').textContent = formatPrice(total);
    }

    // Hàm hiển thị toast notification
    function showToast(toast) {
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: toast.message,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: toast.type === 'success' ? '#28a745' : '#dc3545',
                className: "toast-notification",
                close: true,
                stopOnFocus: true
            }).showToast();
        } else {
            // Fallback nếu không có Toastify
            alert(toast.message);
        }
    }

    // Hàm cập nhật số lượng giỏ hàng ở header
    function updateHeaderCartCount(cartCount = null) {
        // Nếu không truyền cartCount, đếm từ DOM
        const remainingItems = cartCount !== null ? cartCount : document.querySelectorAll('.cart-item').length;

        // Cập nhật số lượng ở header
        const cartCountElements = document.querySelectorAll('.cart-count, [data-cart-count]');
        cartCountElements.forEach(element => {
            element.textContent = remainingItems;
            if (remainingItems > 0) {
                // Giữ nguyên CSS gốc, chỉ reset inline styles
                element.style.display = '';
                element.style.visibility = 'visible';
            } else {
                element.style.visibility = 'hidden';
            }
        });
    }

    // Xóa hoàn toàn hàm updateCartTotal()

    // Biến theo dõi API đang chạy
    let isUpdatingQuantity = false;

    // Hàm cập nhật số lượng với debounce ngắn hơn
    const debouncedUpdateQuantity = debounce((itemId, quantity) => {
        isUpdatingQuantity = true;
        updateCheckoutButtonState(); // Disable nút checkout
        fetch('/api/medical/cart/update-quantity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                item_id: itemId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cart = data.cart;
                if (cart) {
                    cart.items.forEach(item => {
                        const cartItem = document.querySelector(`.cart-item[data-id="${item.id}"]`);
                        if (cartItem) {
                            const currentQuantity = parseInt(cartItem.querySelector('.quantity-input').value);
                            if (currentQuantity !== item.quantity) {
                                cartItem.querySelector('.quantity-input').value = item.quantity;
                                cartItem.querySelector('.product-total').textContent = formatPrice(item.total);
                                updateSelectedItems();
                            }
                        }
                    });
                }
            }
        })
        .catch(error => {
            console.error('API error:', error);
            showToast({
                type: 'error',
                message: 'Có lỗi xảy ra. Vui lòng thử lại.'
            });
        })
        .finally(() => {
            isUpdatingQuantity = false; // Đánh dấu API đã hoàn thành
            updateCheckoutButtonState(); // Cập nhật trạng thái nút checkout
        });
    }, 300); // Giảm xuống 300ms thay vì 1 giây

    // Hàm cập nhật trạng thái nút checkout
    function updateCheckoutButtonState() {
        const checkoutBtn = document.getElementById('checkout-btn');
        if (checkoutBtn) {
            if (isUpdatingQuantity) {
                checkoutBtn.disabled = true;
                checkoutBtn.style.opacity = '0.6';
                checkoutBtn.style.cursor = 'not-allowed';
            } else {
                // Chỉ enable nếu có sản phẩm được chọn
                const checkedItems = document.querySelectorAll('.item-checkbox:checked');
                checkoutBtn.disabled = checkedItems.length === 0;
                checkoutBtn.style.opacity = checkedItems.length === 0 ? '0.6' : '1';
                checkoutBtn.style.cursor = checkedItems.length === 0 ? 'not-allowed' : 'pointer';
            }
        }
    }

    // Xóa các event listener cũ và thêm mới cho quantity buttons
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.replaceWith(btn.cloneNode(true));
    });

    // Thêm event listener mới cho quantity buttons
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const cartItem = btn.closest('.cart-item');
            const input = cartItem.querySelector('.quantity-input');
            const itemId = cartItem.dataset.id;
            let value = parseInt(input.value, 10);

            if (btn.classList.contains('decrease')) {
                if (value > 1) {
                    value--;
                } else {
                    // Hiển thị modal xác nhận khi số lượng là 1 và ấn nút trừ
                    const productName = cartItem.querySelector('.cart-product-name').textContent;
                    ModalCommon.showConfirm(
                        `Bạn có muốn xóa sản phẩm "${productName}" khỏi giỏ hàng?`,
                        function() {
                            deleteItem(itemId);
                        }
                    );
                    return;
                }
            } else {
                value++;
            }

            // Cập nhật UI ngay lập tức
            input.value = value;
            updateItemTotal(cartItem, value);
            updateSelectedItems();

            // Gọi API sau 1 giây
            debouncedUpdateQuantity(itemId, value);
        });
    });

    // 18. Cập nhật khi thay đổi số lượng
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', updateSelectedItems);
    });
});

// Vue component for recently viewed products
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Vue !== 'undefined') {
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    products: [],
                    isLoading: true,
                    isLoggedIn: <?php echo e(Auth::guard('customer')->check() ? 'true' : 'false'); ?>

                }
            },
            mounted() {
                this.fetchRecentlyViewed();
            },
            methods: {
                async fetchRecentlyViewed() {
                    try {
                        const response = await fetch('/api/products/recently-viewed');
                        const data = await response.json();
                        this.products = data.data || [];
                    } catch (error) {
                        console.error('Error fetching recently viewed products:', error);
                    } finally {
                        this.isLoading = false;
                    }
                },
                getProductImage(product) {
                    if (product.images && product.images.length > 0) {
                        return product.images[0].url || '/storage/' + product.images[0].path;
                    }
                    return '/images/product.png';
                },
                getProductName(product) {
                    return product.name || 'Sản phẩm';
                },
                getProductUrl(product) {
                    return '/product/detail?productId=' + product.id;
                },
                hasVisiblePrice(product) {
                    // Sử dụng logic giống medical theme - kiểm tra visible_price flag
                    return product.visible_price && product.price && parseFloat(product.price) > 0;
                },
                hasSpecialPrice(product) {
                    // Kiểm tra có special_price và nhỏ hơn price gốc
                    return product.special_price &&
                           product.price &&
                           parseFloat(product.special_price) < parseFloat(product.price);
                },
                showDiscount(product) {
                    // Chỉ hiển thị discount khi đã đăng nhập và có special price
                    return this.hasSpecialPrice(product);
                },
                getDiscountPercent(product) {
                    if (this.hasSpecialPrice(product)) {
                        const regular = parseFloat(product.price);
                        const special = parseFloat(product.special_price);
                        return Math.round(100 * (regular - special) / regular);
                    }
                    return 0;
                },
                getRegularPrice(product) {
                    return parseFloat(product.price) || 0;
                },
                getFinalPrice(product) {
                    return parseFloat(product.special_price || product.price) || 0;
                },
                formatPrice(price) {
                    return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
                },
                getUnitType(product) {
                    // Default unit type - có thể customize dựa trên product attributes
                    return '1 sản phẩm';
                },


                redirectToLogin() {
                    // Redirect to login page
                    window.location.href = '/signin';
                },
                async addToCart(product) {
                    try {
                        const response = await fetch('/api/medical/cart/add', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                product_id: product.id,
                                quantity: 1
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Hiển thị toast thành công
                            this.showToast({
                                type: 'success',
                                close: true,
                                message: data.message || 'Đã thêm sản phẩm vào giỏ hàng'
                            });

                            // Cập nhật số lượng giỏ hàng nếu có
                            if (data.cart_count !== undefined) {
                                this.updateCartCount(data.cart_count);
                            }
                        } else {
                            this.showToast({
                                type: 'error',
                                close: true,
                                message: data.message || 'Có lỗi xảy ra'
                            });
                        }
                    } catch (error) {
                        console.error('Error adding to cart:', error);
                        this.showToast({
                            type: 'error',
                            close: true,
                            message: 'Có lỗi xảy ra khi thêm vào giỏ hàng'
                        });
                    }
                },
                async requestQuote(product) {
                    try {
                        const response = await fetch('/api/medical/quote', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({
                                product_id: product.id,
                                quantity: 1
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Hiển thị toast thành công
                            this.showToast({
                                type: 'success',
                                close: true,
                                message: data.message || 'Đã thêm sản phẩm vào báo giá'
                            });
                        } else {
                            this.showToast({
                                type: 'error',
                                close: true,
                                message: data.message || 'Có lỗi xảy ra'
                            });
                        }
                    } catch (error) {
                        console.error('Error requesting quote:', error);
                        this.showToast({
                            type: 'error',
                            close: true,
                            message: 'Có lỗi xảy ra khi thêm vào báo giá'
                        });
                    }
                },
                showToast(toast) {
                    if (typeof Toastify !== 'undefined') {
                        Toastify({
                            text: toast.message,
                            duration: 3000,
                            gravity: "top",
                            position: "right",
                            backgroundColor: toast.type === 'success' ? '#F59E0B' : '#EF4444', // Đổi từ xanh lá sang vàng cam
                            close: true
                        }).showToast();
                    }
                },
                updateCartCount(count) {
                    // Cập nhật số lượng giỏ hàng trên header
                    const cartCountElements = document.querySelectorAll('.cart-count, [data-cart-count]');
                    cartCountElements.forEach(element => {
                        element.textContent = count;
                        if (count > 0) {
                            // Giữ nguyên CSS gốc, chỉ reset inline styles
                            element.style.display = '';
                            element.style.visibility = 'visible';
                        } else {
                            element.style.visibility = 'hidden';
                        }
                    });
                }
            }
        });

        const container = document.getElementById('recently-viewed-container');
        if (container) {
            app.mount('#recently-viewed-container');
        }
    }
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/themes/medical/views/cart/cart.blade.php ENDPATH**/ ?>